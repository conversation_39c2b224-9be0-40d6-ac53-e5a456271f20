// Enhanced Ad Configuration
// This file contains all ad-related configuration for the app with improved revenue optimization

import { Platform, Dimensions } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Enhanced Ad placement configuration with more strategic placements
export const AD_PLACEMENTS = {
  // Banner placements
  HOME_BANNER: 'home_banner',
  QUIZ_BANNER: 'quiz_banner',
  CHAPTER_BANNER: 'chapter_banner',
  SETTINGS_BANNER: 'settings_banner',
  AI_CHAT_BANNER: 'ai_chat_banner',
  STUDY_TOOLS_BANNER: 'study_tools_banner',
  FAVORITES_BANNER: 'favorites_banner',
  NOTES_BANNER: 'notes_banner',
  MOCK_EXAMS_BANNER: 'mock_exams_banner',

  // Interstitial placements
  QUIZ_INTERSTITIAL: 'quiz_interstitial',
  CHAPTER_COMPLETE_INTERSTITIAL: 'chapter_complete_interstitial',
  STUDY_SESSION_INTERSTITIAL: 'study_session_interstitial',
  APP_LAUNCH_INTERSTITIAL: 'app_launch_interstitial',
  PDF_DOWNLOAD_INTERSTITIAL: 'pdf_download_interstitial',
  PDF_DOWNLOAD_COMPLETE_INTERSTITIAL: 'pdf_download_complete_interstitial',

  // Native ad placements
  QUIZ_LIST_NATIVE: 'quiz_list_native',
  STUDY_TOOLS_NATIVE: 'study_tools_native',

  // Rewarded ad placements
  HINT_REWARDED: 'hint_rewarded',
  EXTRA_LIVES_REWARDED: 'extra_lives_rewarded',
};

// Enhanced Ad frequency configuration for better user experience and revenue
// Dynamic configuration based on device performance
const getDevicePerformanceLevel = () => {
  // Simple device performance detection
  const { width, height } = Dimensions.get('window');
  const screenArea = width * height;
  const isHighEnd = screenArea > 1000000; // Rough estimate for high-end devices
  const isLowEnd = screenArea < 500000; // Rough estimate for low-end devices

  return {
    isHighEnd,
    isLowEnd,
    isMidRange: !isHighEnd && !isLowEnd
  };
};

const devicePerf = getDevicePerformanceLevel();

export const AD_FREQUENCY = {
  INTERSTITIAL_AFTER_QUIZ: devicePerf.isLowEnd ? 3 : 2, // More lenient on low-end devices
  INTERSTITIAL_MIN_INTERVAL: devicePerf.isLowEnd ? 120000 : 90000, // 2 minutes for low-end, 1.5 for others
  BANNER_REFRESH_INTERVAL: devicePerf.isLowEnd ? 90000 : 60000, // Slower refresh on low-end devices
  APP_LAUNCH_INTERSTITIAL_DELAY: devicePerf.isLowEnd ? 45000 : 30000, // More time for low-end devices
  CHAPTER_COMPLETE_INTERSTITIAL_CHANCE: devicePerf.isLowEnd ? 0.5 : 0.7, // Lower chance on low-end
  STUDY_SESSION_INTERSTITIAL_INTERVAL: 300000, // Keep consistent across devices

  // PDF Download ad settings
  PDF_DOWNLOAD_INTERSTITIAL_CHANCE: 0.8, // 80% chance to show ad when download starts
  PDF_DOWNLOAD_COMPLETE_INTERSTITIAL_CHANCE: 0.6, // 60% chance to show ad when download completes
  PDF_DOWNLOAD_MIN_SIZE_FOR_AD: 1024 * 1024, // Only show ads for downloads > 1MB
  PDF_DOWNLOAD_MIN_DURATION_FOR_AD: 3000, // Only show completion ad if download took > 3 seconds

  // New timeout configurations for better loading
  AD_LOAD_TIMEOUT: devicePerf.isLowEnd ? 15000 : 10000, // Longer timeout for low-end devices
  AD_RETRY_DELAY: devicePerf.isLowEnd ? 5000 : 3000, // Longer retry delay for low-end
  MAX_AD_RETRIES: devicePerf.isLowEnd ? 5 : 8, // Fewer retries on low-end to save resources
};

// Enhanced Ad sizes configuration with adaptive sizing
export const AD_SIZES = {
  SMALL_BANNER: { width: 320, height: 50 },
  MEDIUM_BANNER: { width: 320, height: 100 },
  LARGE_BANNER: { width: 320, height: 250 },
  ADAPTIVE_BANNER: 'adaptive', // Let AdMob decide the best size
  SMART_BANNER: 'smartBanner', // Smart banner that adapts to screen
  FULL_BANNER: { width: 468, height: 60 },
  LEADERBOARD: { width: 728, height: 90 },
  MEDIUM_RECTANGLE: { width: 300, height: 250 },
  // Dynamic sizing based on screen width
  RESPONSIVE_BANNER: {
    width: Math.min(screenWidth - 20, 320),
    height: screenWidth > 400 ? 100 : 50
  },
};

// Test mode configuration
export const AD_TEST_MODE = __DEV__; // Use test ads in development

// Enhanced Ad unit IDs with multiple ad types for better revenue
export const PRODUCTION_AD_UNITS = {
  banner: Platform.select({
    ios: 'YOUR_IOS_BANNER_AD_UNIT_ID',
    android: 'YOUR_ANDROID_BANNER_AD_UNIT_ID',
  }),
  interstitial: Platform.select({
    ios: 'YOUR_IOS_INTERSTITIAL_AD_UNIT_ID',
    android: 'YOUR_ANDROID_INTERSTITIAL_AD_UNIT_ID',
  }),
  // Note: You'll need to create these additional ad units in AdMob console
  rewarded: Platform.select({
    ios: 'ca-app-pub-9706687137550019/4124160377', // Placeholder - create rewarded ad unit
    android: 'ca-app-pub-9706687137550019/4124160377', // Placeholder - create rewarded ad unit
  }),
  native: Platform.select({
    ios: 'ca-app-pub-9706687137550019/4124160377', // Placeholder - create native ad unit
    android: 'ca-app-pub-9706687137550019/4124160377', // Placeholder - create native ad unit
  }),
};

// Enhanced Test ad unit IDs (provided by Google)
export const TEST_AD_UNITS = {
  banner: Platform.select({
    ios: 'ca-app-pub-3940256099942544/2934735716',
    android: 'ca-app-pub-3940256099942544/6300978111',
  }),
  interstitial: Platform.select({
    ios: 'ca-app-pub-3940256099942544/4411468910',
    android: 'ca-app-pub-3940256099942544/1033173712',
  }),
  rewarded: Platform.select({
    ios: 'ca-app-pub-3940256099942544/1712485313',
    android: 'ca-app-pub-3940256099942544/5224354917',
  }),
  native: Platform.select({
    ios: 'ca-app-pub-3940256099942544/3986624511',
    android: 'ca-app-pub-3940256099942544/**********',
  }),
};

// Get the appropriate ad unit ID based on test mode
export const getAdUnitId = (adType) => {
  return AD_TEST_MODE ? TEST_AD_UNITS[adType] : PRODUCTION_AD_UNITS[adType];
};

// Enhanced Ad request configuration for better targeting and revenue
export const AD_REQUEST_CONFIG = {
  requestNonPersonalizedAdsOnly: false, // Set to true for GDPR compliance if needed
  keywords: [
    'education', 'quiz', 'learning', 'study', 'neet', 'exam', 'medical',
    'entrance', 'test', 'preparation', 'biology', 'chemistry', 'physics',
    'student', 'college', 'university', 'career', 'competitive', 'coaching'
  ],
  contentUrl: 'https://quiz-bee-techs.com', // Your app's content URL
  // Enhanced targeting
  maxAdContentRating: 'G', // General audiences
  tagForChildDirectedTreatment: false,
  tagForUnderAgeOfConsent: false,

  // Network-aware configuration
  networkSensitiveLoading: true,
  serverSideVerification: {
    userId: null, // Will be set dynamically
    customData: 'quiz-bee-techs-app'
  }
};

// Enhanced Ad error handling configuration
export const AD_ERROR_CONFIG = {
  maxRetries: 5, // Increased retries for better fill rate
  retryDelay: 3000, // Reduced delay for faster recovery
  exponentialBackoff: true, // Use exponential backoff for retries
  fallbackToWebView: true, // Fall back to WebView ads if native ads fail
  maxConsecutiveErrors: 10, // Disable placement after too many errors
  errorCooldownPeriod: 600000, // 10 minutes cooldown after max errors
};

// Revenue optimization configuration
export const REVENUE_CONFIG = {
  // Ad refresh settings
  enableBannerRefresh: true,
  bannerRefreshInterval: AD_FREQUENCY.BANNER_REFRESH_INTERVAL,
  maxBannerRefreshes: 10, // Limit refreshes per session

  // Interstitial optimization
  enableSmartInterstitials: true,
  interstitialCapping: 5, // Max 5 interstitials per session

  // User engagement tracking
  trackUserEngagement: true,
  highEngagementThreshold: 300000, // 5 minutes of active usage

  // A/B testing for ad placements
  enableAdPlacementTesting: true,
  testVariants: ['default', 'aggressive', 'conservative'],
};

// Ad placement priority for revenue optimization
export const AD_PLACEMENT_PRIORITY = {
  [AD_PLACEMENTS.HOME_BANNER]: 'high',
  [AD_PLACEMENTS.QUIZ_BANNER]: 'high',
  [AD_PLACEMENTS.QUIZ_INTERSTITIAL]: 'critical',
  [AD_PLACEMENTS.CHAPTER_COMPLETE_INTERSTITIAL]: 'high',
  [AD_PLACEMENTS.AI_CHAT_BANNER]: 'medium',
  [AD_PLACEMENTS.STUDY_TOOLS_BANNER]: 'medium',
  [AD_PLACEMENTS.SETTINGS_BANNER]: 'low',
  [AD_PLACEMENTS.HINT_REWARDED]: 'critical',
};
