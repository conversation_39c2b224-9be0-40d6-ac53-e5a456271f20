{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeReleaseResources-79:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495d9b066df23cebe365f0b1f0fc7029\\transformed\\exoplayer-ui-2.18.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,483,692,781,872,951,1049,1146,1225,1291,1397,1504,1569,1635,1699,1771,1891,2014,2136,2211,2299,2372,2452,2543,2636,2702,2766,2819,2879,2927,2988,3059,3130,3197,3275,3340,3399,3465,3530,3596,3648,3708,3782,3856", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,119,122,121,74,87,72,79,90,92,65,63,52,59,47,60,70,70,66,77,64,58,65,64,65,51,59,73,73,53", "endOffsets": "286,478,687,776,867,946,1044,1141,1220,1286,1392,1499,1564,1630,1694,1766,1886,2009,2131,2206,2294,2367,2447,2538,2631,2697,2761,2814,2874,2922,2983,3054,3125,3192,3270,3335,3394,3460,3525,3591,3643,3703,3777,3851,3905"}, "to": {"startLines": "2,11,15,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,341,533,8213,8302,8393,8472,8570,8667,8746,8812,8918,9025,9090,9156,9220,9292,9412,9535,9657,9732,9820,9893,9973,10064,10157,10223,11000,11053,11113,11161,11222,11293,11364,11431,11509,11574,11633,11699,11764,11830,11882,11942,12016,12090", "endLines": "10,14,18,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,119,122,121,74,87,72,79,90,92,65,63,52,59,47,60,70,70,66,77,64,58,65,64,65,51,59,73,73,53", "endOffsets": "336,528,737,8297,8388,8467,8565,8662,8741,8807,8913,9020,9085,9151,9215,9287,9407,9530,9652,9727,9815,9888,9968,10059,10152,10218,10282,11048,11108,11156,11217,11288,11359,11426,11504,11569,11628,11694,11759,11825,11877,11937,12011,12085,12139"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bbb068f5d1da3b4f3e1ef6a35abc59e\\transformed\\foundation-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,94", "endOffsets": "139,234"}, "to": {"startLines": "259,260", "startColumns": "4,4", "startOffsets": "21587,21676", "endColumns": "88,94", "endOffsets": "21671,21766"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0309cb612008fa0a7f120a51a87e98c5\\transformed\\play-services-basement-18.4.0\\res\\values-fr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "160", "endOffsets": "355"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6193", "endColumns": "164", "endOffsets": "6353"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c7a5afab22fc7e54eb8c70c357abcc1\\transformed\\react-android-0.76.9-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,208,278,361,428,507,589,679,771,842,929,1004,1091,1171,1251,1326,1403,1476,1567,1646,1727,1799", "endColumns": "69,82,69,82,66,78,81,89,91,70,86,74,86,79,79,74,76,72,90,78,80,71,79", "endOffsets": "120,203,273,356,423,502,584,674,766,837,924,999,1086,1166,1246,1321,1398,1471,1562,1641,1722,1794,1874"}, "to": {"startLines": "50,64,146,153,157,170,171,225,226,229,237,240,241,242,244,245,247,249,250,252,255,257,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3667,5047,12567,13048,13368,14375,14454,18952,19042,19300,19790,20040,20115,20202,20357,20437,20590,20741,20814,21006,21232,21435,21507", "endColumns": "69,82,69,82,66,78,81,89,91,70,86,74,86,79,79,74,76,72,90,78,80,71,79", "endOffsets": "3732,5125,12632,13126,13430,14449,14531,19037,19129,19366,19872,20110,20197,20277,20432,20507,20662,20809,20900,21080,21308,21502,21582"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "52,53,54,55,56,57,58,251", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3820,3918,4020,4119,4221,4325,4429,20905", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "3913,4015,4114,4216,4320,4424,4542,21001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dc43d647cd71a26bfd564415b69e8be4\\transformed\\play-services-base-18.0.1\\res\\values-fr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,471,597,702,869,998,1115,1224,1398,1506,1687,1819,1975,2150,2219,2282", "endColumns": "101,175,125,104,166,128,116,108,173,107,180,131,155,174,68,62,79", "endOffsets": "294,470,596,701,868,997,1114,1223,1397,1505,1686,1818,1974,2149,2218,2281,2361"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5130,5236,5416,5546,5655,5826,5959,6080,6358,6536,6648,6833,6969,7129,7308,7381,7448", "endColumns": "105,179,129,108,170,132,120,112,177,111,184,135,159,178,72,66,83", "endOffsets": "5231,5411,5541,5650,5821,5954,6075,6188,6531,6643,6828,6964,7124,7303,7376,7443,7527"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\11b33a2f8f4c91a9de31219b5632fe82\\transformed\\Android-Image-Cropper-4.3.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,212,276,325,403,477,540,604,665,741", "endColumns": "109,46,63,48,77,73,62,63,60,75,54", "endOffsets": "160,207,271,320,398,472,535,599,660,736,791"}, "to": {"startLines": "84,85,86,147,148,149,150,151,222,223,224", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7639,7749,7796,12637,12686,12764,12838,12901,18760,18821,18897", "endColumns": "109,46,63,48,77,73,62,63,60,75,54", "endOffsets": "7744,7791,7855,12681,12759,12833,12896,12960,18816,18892,18947"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,321,419,549,634,700,797,880,946,1048,1123,1179,1258,1318,1372,1494,1553,1615,1669,1751,1886,1978,2062,2176,2255,2336,2429,2496,2562,2642,2723,2826,2899,2977,3050,3122,3215,3287,3379,3471,3545,3629,3721,3778,3844,3927,4014,4076,4140,4203,4305,4403,4500,4601", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,97,129,84,65,96,82,65,101,74,55,78,59,53,121,58,61,53,81,134,91,83,113,78,80,92,66,65,79,80,102,72,77,72,71,92,71,91,91,73,83,91,56,65,82,86,61,63,62,101,97,96,100,88", "endOffsets": "233,316,414,544,629,695,792,875,941,1043,1118,1174,1253,1313,1367,1489,1548,1610,1664,1746,1881,1973,2057,2171,2250,2331,2424,2491,2557,2637,2718,2821,2894,2972,3045,3117,3210,3282,3374,3466,3540,3624,3716,3773,3839,3922,4009,4071,4135,4198,4300,4398,4495,4596,4685"}, "to": {"startLines": "19,51,59,60,61,90,142,152,156,158,159,160,161,162,163,164,165,166,167,168,169,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,221", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "742,3737,4547,4645,4775,8147,12144,12965,13302,13435,13537,13612,13668,13747,13807,13861,13983,14042,14104,14158,14240,14536,14628,14712,14826,14905,14986,15079,15146,15212,15292,15373,15476,15549,15627,15700,15772,15865,15937,16029,16121,16195,16279,16371,16428,16494,16577,16664,16726,16790,16853,16955,17053,17150,18671", "endLines": "22,51,59,60,61,90,142,152,156,158,159,160,161,162,163,164,165,166,167,168,169,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,221", "endColumns": "12,82,97,129,84,65,96,82,65,101,74,55,78,59,53,121,58,61,53,81,134,91,83,113,78,80,92,66,65,79,80,102,72,77,72,71,92,71,91,91,73,83,91,56,65,82,86,61,63,62,101,97,96,100,88", "endOffsets": "920,3815,4640,4770,4855,8208,12236,13043,13363,13532,13607,13663,13742,13802,13856,13978,14037,14099,14153,14235,14370,14623,14707,14821,14900,14981,15074,15141,15207,15287,15368,15471,15544,15622,15695,15767,15860,15932,16024,16116,16190,16274,16366,16423,16489,16572,16659,16721,16785,16848,16950,17048,17145,17246,18755"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b56249db71bc4564ba5f782195824c95\\transformed\\exoplayer-core-2.18.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,263,335,418,495,592,685", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "124,189,258,330,413,490,587,680,763"}, "to": {"startLines": "115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10287,10361,10426,10495,10567,10650,10727,10824,10917", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "10356,10421,10490,10562,10645,10722,10819,10912,10995"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,238", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "925,1036,1151,1261,1343,1449,1579,1657,1733,1824,1917,2015,2110,2210,2303,2396,2491,2582,2673,2759,2869,2980,3083,3194,3302,3409,3568,19877", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "1031,1146,1256,1338,1444,1574,1652,1728,1819,1912,2010,2105,2205,2298,2391,2486,2577,2668,2754,2864,2975,3078,3189,3297,3404,3563,3662,19959"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46429bd1dac210616d37d633e605f2e8\\transformed\\browser-1.8.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "83,143,144,145", "startColumns": "4,4,4,4", "startOffsets": "7532,12241,12343,12462", "endColumns": "106,101,118,104", "endOffsets": "7634,12338,12457,12562"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1c6965b61d3737d12e0f0d1e87d21cde\\transformed\\ui-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,392,492,579,658,750,842,929,1010,1095,1171,1246,1324,1398,1476,1545", "endColumns": "98,87,99,99,86,78,91,91,86,80,84,75,74,77,73,77,68,121", "endOffsets": "199,287,387,487,574,653,745,837,924,1005,1090,1166,1241,1319,1393,1471,1540,1662"}, "to": {"startLines": "62,63,87,88,89,154,155,208,209,227,228,239,243,246,248,253,254,256", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4860,4959,7860,7960,8060,13131,13210,17408,17500,19134,19215,19964,20282,20512,20667,21085,21163,21313", "endColumns": "98,87,99,99,86,78,91,91,86,80,84,75,74,77,73,77,68,121", "endOffsets": "4954,5042,7955,8055,8142,13205,13297,17495,17582,19210,19295,20035,20352,20585,20736,21158,21227,21430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\659eba141f87d2afd24e0f28711a233e\\transformed\\play-services-ads-23.6.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,287,344,411,485,594,657,802,920,1042,1092,1150,1282,1384,1432,1527,1563,1598,1654,1735,1775", "endColumns": "41,45,56,66,73,108,62,144,117,121,49,57,131,101,47,94,35,34,55,80,39,55", "endOffsets": "240,286,343,410,484,593,656,801,919,1041,1091,1149,1281,1383,1431,1526,1562,1597,1653,1734,1774,1830"}, "to": {"startLines": "205,206,207,210,211,212,213,214,215,216,217,218,219,220,230,231,232,233,234,235,236,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17251,17297,17347,17587,17658,17736,17849,17916,18065,18187,18313,18367,18429,18565,19371,19423,19522,19562,19601,19661,19746,21771", "endColumns": "45,49,60,70,77,112,66,148,121,125,53,61,135,105,51,98,39,38,59,84,43,59", "endOffsets": "17292,17342,17403,17653,17731,17844,17911,18060,18182,18308,18362,18424,18560,18666,19418,19517,19557,19596,19656,19741,19785,21826"}}]}]}