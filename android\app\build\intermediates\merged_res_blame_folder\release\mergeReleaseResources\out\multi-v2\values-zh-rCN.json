{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeReleaseResources-79:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,272,363,469,542,604,681,740,799,877,934,990,1049,1107,1161,1246,1302,1360,1414,1479,1571,1645,1721,1813,1875,1937,2016,2083,2149,2213,2282,2360,2421,2492,2559,2619,2698,2765,2848,2933,3007,3072,3148,3196,3260,3336,3414,3476,3540,3603,3683,3759,3837,3914", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,69,90,105,72,61,76,58,58,77,56,55,58,57,53,84,55,57,53,64,91,73,75,91,61,61,78,66,65,63,68,77,60,70,66,59,78,66,82,84,73,64,75,47,63,75,77,61,63,62,79,75,77,76,68", "endOffsets": "197,267,358,464,537,599,676,735,794,872,929,985,1044,1102,1156,1241,1297,1355,1409,1474,1566,1640,1716,1808,1870,1932,2011,2078,2144,2208,2277,2355,2416,2487,2554,2614,2693,2760,2843,2928,3002,3067,3143,3191,3255,3331,3409,3471,3535,3598,3678,3754,3832,3909,3978"}, "to": {"startLines": "19,51,59,60,61,90,142,152,157,159,160,161,162,163,164,165,166,167,168,169,170,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "658,3428,4162,4253,4359,6979,10285,10971,11322,11446,11524,11581,11637,11696,11754,11808,11893,11949,12007,12061,12126,12423,12497,12573,12665,12727,12789,12868,12935,13001,13065,13134,13212,13273,13344,13411,13471,13550,13617,13700,13785,13859,13924,14000,14048,14112,14188,14266,14328,14392,14455,14535,14611,14689,15810", "endLines": "22,51,59,60,61,90,142,152,157,159,160,161,162,163,164,165,166,167,168,169,170,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,223", "endColumns": "12,69,90,105,72,61,76,58,58,77,56,55,58,57,53,84,55,57,53,64,91,73,75,91,61,61,78,66,65,63,68,77,60,70,66,59,78,66,82,84,73,64,75,47,63,75,77,61,63,62,79,75,77,76,68", "endOffsets": "800,3493,4248,4354,4427,7036,10357,11025,11376,11519,11576,11632,11691,11749,11803,11888,11944,12002,12056,12121,12213,12492,12568,12660,12722,12784,12863,12930,12996,13060,13129,13207,13268,13339,13406,13466,13545,13612,13695,13780,13854,13919,13995,14043,14107,14183,14261,14323,14387,14450,14530,14606,14684,14761,15874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\11b33a2f8f4c91a9de31219b5632fe82\\transformed\\Android-Image-Cropper-4.3.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,177,229,271,328,383,435,485", "endColumns": "74,46,51,41,56,54,51,49,57", "endOffsets": "125,172,224,266,323,378,430,480,538"}, "to": {"startLines": "84,85,86,147,148,149,150,151,224", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6552,6627,6674,10715,10757,10814,10869,10921,15879", "endColumns": "74,46,51,41,56,54,51,49,57", "endOffsets": "6622,6669,6721,10752,10809,10864,10916,10966,15932"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c7a5afab22fc7e54eb8c70c357abcc1\\transformed\\react-android-0.76.9-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,191,258,324,399,464,529,598,669,742,814,882,953,1026,1098,1175,1251,1323,1393,1462,1540,1608,1679,1746", "endColumns": "65,69,66,65,74,64,64,68,70,72,71,67,70,72,71,76,75,71,69,68,77,67,70,66,68", "endOffsets": "116,186,253,319,394,459,524,593,664,737,809,877,948,1021,1093,1170,1246,1318,1388,1457,1535,1603,1674,1741,1810"}, "to": {"startLines": "50,64,146,153,154,158,171,172,173,225,226,229,237,240,241,242,244,245,247,249,250,252,255,257,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3362,4585,10648,11030,11096,11381,12218,12283,12352,15937,16010,16230,16567,16785,16858,16930,17080,17156,17300,17441,17510,17689,17896,18083,18150", "endColumns": "65,69,66,65,74,64,64,68,70,72,71,67,70,72,71,76,75,71,69,68,77,67,70,66,68", "endOffsets": "3423,4650,10710,11091,11166,11441,12278,12347,12418,16005,16077,16293,16633,16853,16925,17002,17151,17223,17365,17505,17583,17752,17962,18145,18214"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "52,53,54,55,56,57,58,251", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3498,3590,3691,3785,3879,3972,4066,17588", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "3585,3686,3780,3874,3967,4061,4157,17684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b56249db71bc4564ba5f782195824c95\\transformed\\exoplayer-core-2.18.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,225,278,350,404,479,557", "endColumns": "55,55,57,52,71,53,74,77,58", "endOffsets": "106,162,220,273,345,399,474,552,611"}, "to": {"startLines": "115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8691,8747,8803,8861,8914,8986,9040,9115,9193", "endColumns": "55,55,57,52,71,53,74,77,58", "endOffsets": "8742,8798,8856,8909,8981,9035,9110,9188,9247"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dc43d647cd71a26bfd564415b69e8be4\\transformed\\play-services-base-18.0.1\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,419,530,628,729,841,939,1027,1131,1228,1354,1465,1565,1669,1721,1774", "endColumns": "96,124,110,97,100,111,97,87,103,96,125,110,99,103,51,52,69", "endOffsets": "293,418,529,627,728,840,938,1026,1130,1227,1353,1464,1564,1668,1720,1773,1843"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4655,4756,4885,5000,5102,5207,5323,5425,5616,5724,5825,5955,6070,6174,6282,6338,6395", "endColumns": "100,128,114,101,104,115,101,91,107,100,129,114,103,107,55,56,73", "endOffsets": "4751,4880,4995,5097,5202,5318,5420,5512,5719,5820,5950,6065,6169,6277,6333,6390,6464"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1c6965b61d3737d12e0f0d1e87d21cde\\transformed\\ui-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,343,434,511,585,662,740,815,888,963,1031,1104,1176,1247,1320,1386", "endColumns": "76,75,84,90,76,73,76,77,74,72,74,67,72,71,70,72,65,115", "endOffsets": "177,253,338,429,506,580,657,735,810,883,958,1026,1099,1171,1242,1315,1381,1497"}, "to": {"startLines": "62,63,87,88,89,155,156,210,211,227,228,239,243,246,248,253,254,256", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4432,4509,6726,6811,6902,11171,11245,14907,14985,16082,16155,16717,17007,17228,17370,17757,17830,17967", "endColumns": "76,75,84,90,76,73,76,77,74,72,74,67,72,71,70,72,65,115", "endOffsets": "4504,4580,6806,6897,6974,11240,11317,14980,15055,16150,16225,16780,17075,17295,17436,17825,17891,18078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495d9b066df23cebe365f0b1f0fc7029\\transformed\\exoplayer-ui-2.18.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,450,608,678,747,815,892,968,1022,1084,1158,1232,1294,1355,1414,1480,1568,1651,1739,1802,1869,1934,1988,2062,2135,2196,2258,2310,2368,2415,2476,2533,2595,2652,2713,2769,2824,2887,2949,3012,3061,3112,3177,3242", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,69,68,67,76,75,53,61,73,73,61,60,58,65,87,82,87,62,66,64,53,73,72,60,61,51,57,46,60,56,61,56,60,55,54,62,61,62,48,50,64,64,48", "endOffsets": "282,445,603,673,742,810,887,963,1017,1079,1153,1227,1289,1350,1409,1475,1563,1646,1734,1797,1864,1929,1983,2057,2130,2191,2253,2305,2363,2410,2471,2528,2590,2647,2708,2764,2819,2882,2944,3007,3056,3107,3172,3237,3286"}, "to": {"startLines": "2,11,15,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,500,7041,7111,7180,7248,7325,7401,7455,7517,7591,7665,7727,7788,7847,7913,8001,8084,8172,8235,8302,8367,8421,8495,8568,8629,9252,9304,9362,9409,9470,9527,9589,9646,9707,9763,9818,9881,9943,10006,10055,10106,10171,10236", "endLines": "10,14,18,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "endColumns": "17,12,12,69,68,67,76,75,53,61,73,73,61,60,58,65,87,82,87,62,66,64,53,73,72,60,61,51,57,46,60,56,61,56,60,55,54,62,61,62,48,50,64,64,48", "endOffsets": "332,495,653,7106,7175,7243,7320,7396,7450,7512,7586,7660,7722,7783,7842,7908,7996,8079,8167,8230,8297,8362,8416,8490,8563,8624,8686,9299,9357,9404,9465,9522,9584,9641,9702,9758,9813,9876,9938,10001,10050,10101,10166,10231,10280"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bbb068f5d1da3b4f3e1ef6a35abc59e\\transformed\\foundation-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,136", "endColumns": "80,76", "endOffsets": "131,208"}, "to": {"startLines": "259,260", "startColumns": "4,4", "startOffsets": "18219,18300", "endColumns": "80,76", "endOffsets": "18295,18372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\659eba141f87d2afd24e0f28711a233e\\transformed\\play-services-ads-23.6.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "203,242,285,332,392,453,521,581,652,724,810,860,911,980,1038,1070,1113,1143,1173,1207,1247,1279", "endColumns": "38,42,46,59,60,67,59,70,71,85,49,50,68,57,31,42,29,29,33,39,31,55", "endOffsets": "241,284,331,391,452,520,580,651,723,809,859,910,979,1037,1069,1112,1142,1172,1206,1246,1278,1334"}, "to": {"startLines": "207,208,209,212,213,214,215,216,217,218,219,220,221,222,230,231,232,233,234,235,236,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14766,14809,14856,15060,15124,15189,15261,15325,15400,15476,15566,15620,15675,15748,16298,16334,16381,16415,16449,16487,16531,18377", "endColumns": "42,46,50,63,64,71,63,74,75,89,53,54,72,61,35,46,33,33,37,43,35,59", "endOffsets": "14804,14851,14902,15119,15184,15256,15320,15395,15471,15561,15615,15670,15743,15805,16329,16376,16410,16444,16482,16526,16562,18432"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46429bd1dac210616d37d633e605f2e8\\transformed\\browser-1.8.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "83,143,144,145", "startColumns": "4,4,4,4", "startOffsets": "6469,10362,10454,10555", "endColumns": "82,91,100,92", "endOffsets": "6547,10449,10550,10643"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,238", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "805,900,995,1095,1177,1274,1380,1457,1532,1623,1716,1813,1909,2003,2096,2191,2283,2374,2465,2543,2639,2734,2829,2926,3022,3120,3268,16638", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "895,990,1090,1172,1269,1375,1452,1527,1618,1711,1808,1904,1998,2091,2186,2278,2369,2460,2538,2634,2729,2824,2921,3017,3115,3263,3357,16712"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0309cb612008fa0a7f120a51a87e98c5\\transformed\\play-services-basement-18.4.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "94", "endOffsets": "293"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "5517", "endColumns": "98", "endOffsets": "5611"}}]}]}