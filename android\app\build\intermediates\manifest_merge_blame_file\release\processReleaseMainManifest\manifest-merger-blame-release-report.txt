1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.gokul719.snack97152fc1f368437dac54171df4ba22bd"
4    android:versionCode="5"
5    android:versionName="1.3.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.CAMERA" />
11-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:2:3-62
11-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:2:20-60
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:3:3-64
12-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:3:20-62
13    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
13-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:4:3-77
13-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:4:20-75
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:5:3-77
14-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:5:20-75
15    <uses-permission android:name="android.permission.RECORD_AUDIO" />
15-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:6:3-68
15-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:6:20-66
16    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
16-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:7:3-75
16-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:7:20-73
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:8:3-63
17-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:8:20-61
18    <uses-permission android:name="android.permission.WAKE_LOCK" />
18-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:9:3-65
18-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:9:20-63
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:10:3-78
19-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:10:20-76
20    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
20-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:11:3-76
20-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:11:20-74
21
22    <queries>
22-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:12:3-18:13
23        <intent>
23-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:13:5-17:14
24            <action android:name="android.intent.action.VIEW" />
24-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:7-58
24-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:15-56
25
26            <category android:name="android.intent.category.BROWSABLE" />
26-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-67
26-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:17-65
27
28            <data android:scheme="https" />
28-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
28-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:13-35
29        </intent>
30        <!-- Query open documents -->
31        <intent>
31-->[:expo-document-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:9-17:18
32            <action android:name="android.intent.action.OPEN_DOCUMENT" />
32-->[:expo-document-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
32-->[:expo-document-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:21-71
33
34            <category android:name="android.intent.category.DEFAULT" />
34-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:34:9-67
34-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:34:19-65
35            <category android:name="android.intent.category.OPENABLE" />
35-->[:expo-document-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-73
35-->[:expo-document-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:23-70
36
37            <data android:mimeType="*/*" />
37-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
38        </intent> <!-- Query open documents -->
39        <intent>
39-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-17:18
40            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
40-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-79
40-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:21-76
41        </intent>
42        <intent>
42-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-19:18
43
44            <!-- Required for picking images from the camera roll if targeting API 30 -->
45            <action android:name="android.media.action.IMAGE_CAPTURE" />
45-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-73
45-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:21-70
46        </intent>
47        <intent>
47-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:9-24:18
48
49            <!-- Required for picking images from the camera if targeting API 30 -->
50            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
50-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-80
50-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:21-77
51        </intent>
52        <intent>
52-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-14:18
53
54            <!-- Required for file sharing if targeting API 30 -->
55            <action android:name="android.intent.action.SEND" />
55-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-65
55-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:21-62
56
57            <data android:mimeType="*/*" />
57-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
58        </intent>
59        <intent>
59-->[:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:18
60
61            <!-- Required for text-to-speech if targeting API 30 -->
62            <action android:name="android.intent.action.TTS_SERVICE" />
62-->[:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-72
62-->[:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:21-69
63        </intent>
64        <intent>
64-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:10:9-16:18
65            <action android:name="android.intent.action.GET_CONTENT" />
65-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:13-72
65-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:21-69
66
67            <category android:name="android.intent.category.OPENABLE" />
67-->[:expo-document-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-73
67-->[:expo-document-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:23-70
68
69            <data android:mimeType="*/*" />
69-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
70        </intent> <!-- End of browser content -->
71        <!-- For CustomTabsService -->
72        <intent>
72-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:47:9-49:18
73            <action android:name="android.support.customtabs.action.CustomTabsService" />
73-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:48:13-90
73-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:48:21-87
74        </intent> <!-- End of CustomTabsService -->
75        <!-- For MRAID capabilities -->
76        <intent>
76-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:52:9-56:18
77            <action android:name="android.intent.action.INSERT" />
77-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:53:13-67
77-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:53:21-64
78
79            <data android:mimeType="vnd.android.cursor.dir/event" />
79-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
80        </intent>
81        <intent>
81-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:57:9-61:18
82            <action android:name="android.intent.action.VIEW" />
82-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:7-58
82-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:15-56
83
84            <data android:scheme="sms" />
84-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
84-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:13-35
85        </intent>
86        <intent>
86-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:62:9-66:18
87            <action android:name="android.intent.action.DIAL" />
87-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:63:13-65
87-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:63:21-62
88
89            <data android:path="tel:" />
89-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
90        </intent>
91    </queries>
92
93    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
93-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-79
93-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:22-76
94    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
94-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-81
94-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-78
95    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
95-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-77
95-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-74
96    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
96-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:27:5-82
96-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:27:22-79
97    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
97-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:28:5-88
97-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:28:22-85
98    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
98-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:29:5-83
98-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:29:22-80
99    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
99-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
99-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
100    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
100-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
100-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
101
102    <permission
102-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
103        android:name="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
103-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
104        android:protectionLevel="signature" />
104-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
105
106    <uses-permission android:name="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
106-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
106-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
107    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- for android -->
107-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65e83cd3aee2760cef02a821b313c704\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
107-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65e83cd3aee2760cef02a821b313c704\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
108    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
109    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
110    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
111    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
112    <!-- for Samsung -->
113    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
113-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
113-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
114    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
114-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
114-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
115    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
115-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
115-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
116    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
116-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
116-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
117    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
117-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
117-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
118    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
118-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
118-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
119    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
119-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
119-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
120    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
120-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
120-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
121    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
121-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
121-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
122    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
122-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
122-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
123    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
123-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
123-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
124    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
124-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
124-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
125    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
125-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
125-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
126    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
126-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
126-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
127    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
127-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
127-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
128    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
128-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
128-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
129
130    <application
130-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:3-40:17
131        android:name="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.MainApplication"
131-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:16-47
132        android:allowBackup="true"
132-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:162-188
133        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
133-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:248-316
134        android:extractNativeLibs="false"
135        android:icon="@mipmap/ic_launcher"
135-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:81-115
136        android:label="@string/app_name"
136-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:48-80
137        android:roundIcon="@mipmap/ic_launcher_round"
137-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:116-161
138        android:supportsRtl="true"
138-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:221-247
139        android:theme="@style/AppTheme" >
139-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:189-220
140        <meta-data
140-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:5-159
141            android:name="com.google.android.gms.ads.APPLICATION_ID"
141-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:16-72
142            android:value="ca-app-pub-9706687137550019~9208363455" />
142-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:73-127
143        <meta-data
143-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:21:5-137
144            android:name="com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT"
144-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:21:16-84
145            android:value="true" />
145-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:21:85-105
146        <meta-data
146-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:22:5-135
147            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
147-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:22:16-82
148            android:value="true" />
148-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:22:83-103
149        <meta-data
149-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:5-139
150            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
150-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:16-86
151            android:value="true" />
151-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:87-107
152        <meta-data
152-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:5-83
153            android:name="expo.modules.updates.ENABLED"
153-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:16-59
154            android:value="false" />
154-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:60-81
155        <meta-data
155-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:25:5-105
156            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
156-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:25:16-80
157            android:value="ALWAYS" />
157-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:25:81-103
158        <meta-data
158-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:26:5-99
159            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
159-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:26:16-79
160            android:value="0" />
160-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:26:80-97
161
162        <activity
162-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:5-39:16
163            android:name="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.MainActivity"
163-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:15-43
164            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
164-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:44-134
165            android:exported="true"
165-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:256-279
166            android:launchMode="singleTask"
166-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:135-166
167            android:screenOrientation="portrait"
167-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:280-316
168            android:theme="@style/Theme.App.SplashScreen"
168-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:210-255
169            android:windowSoftInputMode="adjustResize" >
169-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:167-209
170            <intent-filter>
170-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:28:7-31:23
171                <action android:name="android.intent.action.MAIN" />
171-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:29:9-60
171-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:29:17-58
172
173                <category android:name="android.intent.category.LAUNCHER" />
173-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:30:9-68
173-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:30:19-66
174            </intent-filter>
175            <intent-filter>
175-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:32:7-38:23
176                <action android:name="android.intent.action.VIEW" />
176-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:7-58
176-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:15-56
177
178                <category android:name="android.intent.category.DEFAULT" />
178-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:34:9-67
178-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:34:19-65
179                <category android:name="android.intent.category.BROWSABLE" />
179-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-67
179-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:17-65
180
181                <data android:scheme="com.gokul719.snack97152fc1f368437dac54171df4ba22bd" />
181-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
181-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:13-35
182                <data android:scheme="exp+snack-97152fc1-f368-437d-ac54-171df4ba22bd" />
182-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
182-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:13-35
183            </intent-filter>
184        </activity>
185        <!--
186           This may generate a warning during your build:
187
188           > property#android.adservices.AD_SERVICES_CONFIG@android:resource
189           > was tagged at AndroidManifest.xml:23 to replace other declarations
190           > but no other declaration present
191
192           You may safely ignore this warning.
193
194           We must include this in case you also use Firebase Analytics in some
195           of its configurations, as it may also include this file, and the two
196           will collide and cause a build error if we don't set this one to take
197           priority via replacement.
198
199           https://github.com/invertase/react-native-google-mobile-ads/issues/657
200        -->
201        <property
201-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:9-45:48
202            android:name="android.adservices.AD_SERVICES_CONFIG"
202-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:13-65
203            android:resource="@xml/gma_ad_services_config" />
203-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-59
204
205        <provider
205-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-16:20
206            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
206-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-83
207            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.fileprovider"
207-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-64
208            android:exported="false"
208-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-37
209            android:grantUriPermissions="true" >
209-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-47
210            <meta-data
210-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
211                android:name="android.support.FILE_PROVIDER_PATHS"
211-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
212                android:resource="@xml/file_provider_paths" />
212-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
213        </provider>
214        <provider
214-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-15:20
215            android:name="expo.modules.clipboard.ClipboardFileProvider"
215-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-72
216            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.ClipboardFileProvider"
216-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-73
217            android:exported="true" >
217-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-36
218            <meta-data
218-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-14:68
219                android:name="expo.modules.clipboard.CLIPBOARD_FILE_PROVIDER_PATHS"
219-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:17-84
220                android:resource="@xml/clipboard_provider_paths" />
220-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-65
221        </provider>
222        <provider
222-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:9-30:20
223            android:name="expo.modules.filesystem.FileSystemFileProvider"
223-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-74
224            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.FileSystemFileProvider"
224-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-74
225            android:exported="false"
225-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-37
226            android:grantUriPermissions="true" >
226-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:13-47
227            <meta-data
227-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
228                android:name="android.support.FILE_PROVIDER_PATHS"
228-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
229                android:resource="@xml/file_system_provider_paths" />
229-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
230        </provider>
231
232        <service
232-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:9-40:19
233            android:name="com.google.android.gms.metadata.ModuleDependencies"
233-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-78
234            android:enabled="false"
234-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-36
235            android:exported="false" >
235-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:13-37
236            <intent-filter>
236-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:13-35:29
237                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
237-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:17-94
237-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:25-91
238            </intent-filter>
239
240            <meta-data
240-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:13-39:36
241                android:name="photopicker_activity:0:required"
241-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:17-63
242                android:value="" />
242-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:17-33
243        </service>
244
245        <activity
245-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:9-44:59
246            android:name="com.canhub.cropper.CropImageActivity"
246-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:13-64
247            android:exported="true"
247-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:35:13-36
248            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
248-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-56
249        <provider
249-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:46:9-54:20
250            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
250-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:47:13-89
251            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.ImagePickerFileProvider"
251-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:48:13-75
252            android:exported="false"
252-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:49:13-37
253            android:grantUriPermissions="true" >
253-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:50:13-47
254            <meta-data
254-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
255                android:name="android.support.FILE_PROVIDER_PATHS"
255-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
256                android:resource="@xml/image_picker_provider_paths" />
256-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
257        </provider>
258
259        <service
259-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:9-17:19
260            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
260-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-91
261            android:exported="false" >
261-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-37
262            <intent-filter android:priority="-1" >
262-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:29
262-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:28-49
263                <action android:name="com.google.firebase.MESSAGING_EVENT" />
263-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-78
263-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:25-75
264            </intent-filter>
265        </service>
266
267        <receiver
267-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:20
268            android:name="expo.modules.notifications.service.NotificationsService"
268-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-83
269            android:enabled="true"
269-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-35
270            android:exported="false" >
270-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
271            <intent-filter android:priority="-1" >
271-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-30:29
271-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:28-49
272                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
272-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:17-88
272-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:25-85
273                <action android:name="android.intent.action.BOOT_COMPLETED" />
273-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-79
273-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-76
274                <action android:name="android.intent.action.REBOOT" />
274-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:17-71
274-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:25-68
275                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
275-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:17-82
275-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:25-79
276                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
276-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:17-82
276-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:25-79
277                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
277-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-84
277-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:25-81
278            </intent-filter>
279        </receiver>
280
281        <activity
281-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:9-40:75
282            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
282-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:13-92
283            android:excludeFromRecents="true"
283-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:35:13-46
284            android:exported="false"
284-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:13-37
285            android:launchMode="standard"
285-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:13-42
286            android:noHistory="true"
286-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:13-37
287            android:taskAffinity=""
287-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:13-36
288            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
288-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:40:13-72
289
290        <provider
290-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:9-26:20
291            android:name="expo.modules.sharing.SharingFileProvider"
291-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-68
292            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.SharingFileProvider"
292-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-71
293            android:exported="false"
293-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-37
294            android:grantUriPermissions="true" >
294-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-47
295            <meta-data
295-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
296                android:name="android.support.FILE_PROVIDER_PATHS"
296-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
297                android:resource="@xml/sharing_provider_paths" />
297-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
298        </provider>
299
300        <meta-data
300-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-11:89
301            android:name="org.unimodules.core.AppLoader#react-native-headless"
301-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-79
302            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
302-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-86
303        <meta-data
303-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-15:45
304            android:name="com.facebook.soloader.enabled"
304-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-57
305            android:value="true" />
305-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-33
306        <meta-data
306-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1dc3cf6a41b4f5c2729b5d5e6d5ceec\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:11:9-13:43
307            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
307-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1dc3cf6a41b4f5c2729b5d5e6d5ceec\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:12:13-84
308            android:value="GlideModule" />
308-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1dc3cf6a41b4f5c2729b5d5e6d5ceec\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:13:13-40
309
310        <provider
310-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:23:9-31:20
311            android:name="com.canhub.cropper.CropFileProvider"
311-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:24:13-63
312            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.cropper.fileprovider"
312-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:25:13-72
313            android:exported="false"
313-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:26:13-37
314            android:grantUriPermissions="true" >
314-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:27:13-47
315            <meta-data
315-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
316                android:name="android.support.FILE_PROVIDER_PATHS"
316-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
317                android:resource="@xml/library_file_paths" />
317-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
318        </provider> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
319        <activity
319-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:73:9-78:43
320            android:name="com.google.android.gms.ads.AdActivity"
320-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:74:13-65
321            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
321-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:75:13-122
322            android:exported="false"
322-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:76:13-37
323            android:theme="@android:style/Theme.Translucent" />
323-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:77:13-61
324
325        <provider
325-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:80:9-85:43
326            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
326-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:81:13-76
327            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.mobileadsinitprovider"
327-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:82:13-73
328            android:exported="false"
328-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:83:13-37
329            android:initOrder="100" />
329-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:84:13-36
330
331        <service
331-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:87:9-91:43
332            android:name="com.google.android.gms.ads.AdService"
332-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:88:13-64
333            android:enabled="true"
333-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:89:13-35
334            android:exported="false" />
334-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:90:13-37
335
336        <activity
336-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:93:9-97:43
337            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
337-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:94:13-82
338            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
338-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:95:13-122
339            android:exported="false" />
339-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:96:13-37
340        <activity
340-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:98:9-105:43
341            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
341-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:99:13-82
342            android:excludeFromRecents="true"
342-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:100:13-46
343            android:exported="false"
343-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:101:13-37
344            android:launchMode="singleTask"
344-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:102:13-44
345            android:taskAffinity=""
345-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:103:13-36
346            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
346-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:104:13-72
347
348        <receiver
348-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
349            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
349-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
350            android:exported="true"
350-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
351            android:permission="com.google.android.c2dm.permission.SEND" >
351-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
352            <intent-filter>
352-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
353                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
353-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
353-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
354            </intent-filter>
355
356            <meta-data
356-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
357                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
357-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
358                android:value="true" />
358-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
359        </receiver>
360        <!--
361             FirebaseMessagingService performs security checks at runtime,
362             but set to not exported to explicitly avoid allowing another app to call it.
363        -->
364        <service
364-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
365            android:name="com.google.firebase.messaging.FirebaseMessagingService"
365-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
366            android:directBootAware="true"
366-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
367            android:exported="false" >
367-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
368            <intent-filter android:priority="-500" >
368-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:29
368-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:28-49
369                <action android:name="com.google.firebase.MESSAGING_EVENT" />
369-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-78
369-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:25-75
370            </intent-filter>
371        </service>
372        <service
372-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
373            android:name="com.google.firebase.components.ComponentDiscoveryService"
373-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
374            android:directBootAware="true"
374-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
375            android:exported="false" >
375-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
376            <meta-data
376-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
377                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
377-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
378                android:value="com.google.firebase.components.ComponentRegistrar" />
378-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
379            <meta-data
379-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
380                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
380-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
381                android:value="com.google.firebase.components.ComponentRegistrar" />
381-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
382            <meta-data
382-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
383                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
383-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
384                android:value="com.google.firebase.components.ComponentRegistrar" />
384-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
385            <meta-data
385-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
386                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
386-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
387                android:value="com.google.firebase.components.ComponentRegistrar" />
387-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
388            <meta-data
388-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91477e5d6c9216b70ac507e0b5e1f66e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
389                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
389-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91477e5d6c9216b70ac507e0b5e1f66e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
390                android:value="com.google.firebase.components.ComponentRegistrar" />
390-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91477e5d6c9216b70ac507e0b5e1f66e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
391            <meta-data
391-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
392                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
392-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
393                android:value="com.google.firebase.components.ComponentRegistrar" />
393-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
394            <meta-data
394-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10cf65477e02ebee6ddd7715742cb3aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
395                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
395-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10cf65477e02ebee6ddd7715742cb3aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
396                android:value="com.google.firebase.components.ComponentRegistrar" />
396-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10cf65477e02ebee6ddd7715742cb3aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
397        </service>
398
399        <provider
399-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
400            android:name="com.google.firebase.provider.FirebaseInitProvider"
400-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
401            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.firebaseinitprovider"
401-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
402            android:directBootAware="true"
402-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
403            android:exported="false"
403-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
404            android:initOrder="100" />
404-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
405
406        <activity
406-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc43d647cd71a26bfd564415b69e8be4\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
407            android:name="com.google.android.gms.common.api.GoogleApiActivity"
407-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc43d647cd71a26bfd564415b69e8be4\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
408            android:exported="false"
408-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc43d647cd71a26bfd564415b69e8be4\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
409            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
409-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc43d647cd71a26bfd564415b69e8be4\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
410
411        <provider
411-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
412            android:name="androidx.startup.InitializationProvider"
412-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
413            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.androidx-startup"
413-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
414            android:exported="false" >
414-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
415            <meta-data
415-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
416                android:name="androidx.work.WorkManagerInitializer"
416-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
417                android:value="androidx.startup" />
417-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
418            <meta-data
418-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867f28603a9adbd922e925087114cbc1\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
419                android:name="androidx.emoji2.text.EmojiCompatInitializer"
419-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867f28603a9adbd922e925087114cbc1\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
420                android:value="androidx.startup" />
420-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867f28603a9adbd922e925087114cbc1\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
421            <meta-data
421-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad10a3280ed9ff2393dd52d1764f5de6\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
422                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
422-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad10a3280ed9ff2393dd52d1764f5de6\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
423                android:value="androidx.startup" />
423-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad10a3280ed9ff2393dd52d1764f5de6\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
424            <meta-data
424-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
425                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
425-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
426                android:value="androidx.startup" />
426-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
427        </provider>
428
429        <service
429-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
430            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
430-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
431            android:directBootAware="false"
431-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
432            android:enabled="@bool/enable_system_alarm_service_default"
432-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
433            android:exported="false" />
433-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
434        <service
434-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
435            android:name="androidx.work.impl.background.systemjob.SystemJobService"
435-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
436            android:directBootAware="false"
436-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
437            android:enabled="@bool/enable_system_job_service_default"
437-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
438            android:exported="true"
438-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
439            android:permission="android.permission.BIND_JOB_SERVICE" />
439-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
440        <service
440-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
441            android:name="androidx.work.impl.foreground.SystemForegroundService"
441-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
442            android:directBootAware="false"
442-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
443            android:enabled="@bool/enable_system_foreground_service_default"
443-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
444            android:exported="false" />
444-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
445
446        <receiver
446-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
447            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
447-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
448            android:directBootAware="false"
448-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
449            android:enabled="true"
449-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
450            android:exported="false" />
450-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
451        <receiver
451-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
452            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
452-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
453            android:directBootAware="false"
453-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
454            android:enabled="false"
454-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
455            android:exported="false" >
455-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
456            <intent-filter>
456-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
457                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
457-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
457-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
458                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
458-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
458-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
459            </intent-filter>
460        </receiver>
461        <receiver
461-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
462            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
462-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
463            android:directBootAware="false"
463-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
464            android:enabled="false"
464-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
465            android:exported="false" >
465-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
466            <intent-filter>
466-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
467                <action android:name="android.intent.action.BATTERY_OKAY" />
467-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
467-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
468                <action android:name="android.intent.action.BATTERY_LOW" />
468-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
468-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
469            </intent-filter>
470        </receiver>
471        <receiver
471-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
472            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
472-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
473            android:directBootAware="false"
473-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
474            android:enabled="false"
474-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
475            android:exported="false" >
475-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
476            <intent-filter>
476-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
477                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
477-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
477-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
478                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
478-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
478-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
479            </intent-filter>
480        </receiver>
481        <receiver
481-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
482            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
482-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
483            android:directBootAware="false"
483-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
484            android:enabled="false"
484-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
485            android:exported="false" >
485-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
486            <intent-filter>
486-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
487                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
487-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
487-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
488            </intent-filter>
489        </receiver>
490        <receiver
490-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
491            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
491-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
492            android:directBootAware="false"
492-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
493            android:enabled="false"
493-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
494            android:exported="false" >
494-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
495            <intent-filter>
495-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
496                <action android:name="android.intent.action.BOOT_COMPLETED" />
496-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-79
496-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-76
497                <action android:name="android.intent.action.TIME_SET" />
497-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
497-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
498                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
498-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
498-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
499            </intent-filter>
500        </receiver>
501        <receiver
501-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
502            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
502-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
503            android:directBootAware="false"
503-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
504            android:enabled="@bool/enable_system_alarm_service_default"
504-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
505            android:exported="false" >
505-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
506            <intent-filter>
506-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
507                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
507-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
507-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
508            </intent-filter>
509        </receiver>
510        <receiver
510-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
511            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
511-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
512            android:directBootAware="false"
512-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
513            android:enabled="true"
513-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
514            android:exported="true"
514-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
515            android:permission="android.permission.DUMP" >
515-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
516            <intent-filter>
516-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
517                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
517-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
517-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
518            </intent-filter>
519        </receiver>
520
521        <uses-library
521-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa2d5bf76eb464c8ff4defe2fea1aac0\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
522            android:name="android.ext.adservices"
522-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa2d5bf76eb464c8ff4defe2fea1aac0\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
523            android:required="false" />
523-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa2d5bf76eb464c8ff4defe2fea1aac0\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
524
525        <meta-data
525-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0309cb612008fa0a7f120a51a87e98c5\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
526            android:name="com.google.android.gms.version"
526-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0309cb612008fa0a7f120a51a87e98c5\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
527            android:value="@integer/google_play_services_version" />
527-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0309cb612008fa0a7f120a51a87e98c5\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
528
529        <receiver
529-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
530            android:name="androidx.profileinstaller.ProfileInstallReceiver"
530-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
531            android:directBootAware="false"
531-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
532            android:enabled="true"
532-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
533            android:exported="true"
533-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
534            android:permission="android.permission.DUMP" >
534-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
535            <intent-filter>
535-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
536                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
536-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
536-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
537            </intent-filter>
538            <intent-filter>
538-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
539                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
539-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
539-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
540            </intent-filter>
541            <intent-filter>
541-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
542                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
542-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
542-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
543            </intent-filter>
544            <intent-filter>
544-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
545                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
545-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
545-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
546            </intent-filter>
547        </receiver>
548
549        <service
549-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
550            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
550-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
551            android:exported="false" >
551-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
552            <meta-data
552-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
553                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
553-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
554                android:value="cct" />
554-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
555        </service>
556        <service
556-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
557            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
557-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
558            android:exported="false"
558-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
559            android:permission="android.permission.BIND_JOB_SERVICE" >
559-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
560        </service>
561
562        <receiver
562-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
563            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
563-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
564            android:exported="false" />
564-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
565
566        <service
566-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
567            android:name="androidx.room.MultiInstanceInvalidationService"
567-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
568            android:directBootAware="true"
568-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
569            android:exported="false" />
569-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
570    </application>
571
572</manifest>
