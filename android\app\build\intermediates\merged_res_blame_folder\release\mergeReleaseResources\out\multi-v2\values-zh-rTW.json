{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeReleaseResources-79:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "52,53,54,55,56,57,58,251", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3504,3596,3695,3789,3883,3976,4069,17662", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3591,3690,3784,3878,3971,4064,4160,17758"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b56249db71bc4564ba5f782195824c95\\transformed\\exoplayer-core-2.18.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,224,277,347,401,477,555", "endColumns": "55,55,56,52,69,53,75,77,58", "endOffsets": "106,162,219,272,342,396,472,550,609"}, "to": {"startLines": "115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8738,8794,8850,8907,8960,9030,9084,9160,9238", "endColumns": "55,55,56,52,69,53,75,77,58", "endOffsets": "8789,8845,8902,8955,9025,9079,9155,9233,9292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495d9b066df23cebe365f0b1f0fc7029\\transformed\\exoplayer-ui-2.18.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,450,608,689,769,837,915,992,1048,1109,1183,1257,1319,1380,1439,1504,1592,1677,1765,1828,1895,1960,2016,2090,2163,2224,2287,2339,2397,2444,2505,2562,2624,2681,2742,2798,2853,2916,2978,3041,3090,3142,3208,3274", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,79,67,77,76,55,60,73,73,61,60,58,64,87,84,87,62,66,64,55,73,72,60,62,51,57,46,60,56,61,56,60,55,54,62,61,62,48,51,65,65,48", "endOffsets": "282,445,603,684,764,832,910,987,1043,1104,1178,1252,1314,1375,1434,1499,1587,1672,1760,1823,1890,1955,2011,2085,2158,2219,2282,2334,2392,2439,2500,2557,2619,2676,2737,2793,2848,2911,2973,3036,3085,3137,3203,3269,3318"}, "to": {"startLines": "2,11,15,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,500,7059,7140,7220,7288,7366,7443,7499,7560,7634,7708,7770,7831,7890,7955,8043,8128,8216,8279,8346,8411,8467,8541,8614,8675,9297,9349,9407,9454,9515,9572,9634,9691,9752,9808,9863,9926,9988,10051,10100,10152,10218,10284", "endLines": "10,14,18,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "endColumns": "17,12,12,80,79,67,77,76,55,60,73,73,61,60,58,64,87,84,87,62,66,64,55,73,72,60,62,51,57,46,60,56,61,56,60,55,54,62,61,62,48,51,65,65,48", "endOffsets": "332,495,653,7135,7215,7283,7361,7438,7494,7555,7629,7703,7765,7826,7885,7950,8038,8123,8211,8274,8341,8406,8462,8536,8609,8670,8733,9344,9402,9449,9510,9567,9629,9686,9747,9803,9858,9921,9983,10046,10095,10147,10213,10279,10328"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0309cb612008fa0a7f120a51a87e98c5\\transformed\\play-services-basement-18.4.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "98", "endOffsets": "297"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "5528", "endColumns": "102", "endOffsets": "5626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bbb068f5d1da3b4f3e1ef6a35abc59e\\transformed\\foundation-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,78", "endOffsets": "133,212"}, "to": {"startLines": "259,260", "startColumns": "4,4", "startOffsets": "18295,18378", "endColumns": "82,78", "endOffsets": "18373,18452"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,238", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "805,900,993,1093,1175,1272,1380,1457,1532,1624,1718,1815,1911,2006,2100,2196,2288,2380,2472,2550,2646,2741,2836,2933,3029,3127,3277,16710", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "895,988,1088,1170,1267,1375,1452,1527,1619,1713,1810,1906,2001,2095,2191,2283,2375,2467,2545,2641,2736,2831,2928,3024,3122,3272,3366,16784"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1c6965b61d3737d12e0f0d1e87d21cde\\transformed\\ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,345,436,514,588,665,743,818,891,966,1034,1107,1179,1250,1324,1392", "endColumns": "76,75,86,90,77,73,76,77,74,72,74,67,72,71,70,73,67,115", "endOffsets": "177,253,340,431,509,583,660,738,813,886,961,1029,1102,1174,1245,1319,1387,1503"}, "to": {"startLines": "62,63,87,88,89,155,156,210,211,227,228,239,243,246,248,253,254,256", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4439,4516,6741,6828,6919,11221,11295,14958,15036,16153,16226,16789,17079,17300,17442,17831,17905,18043", "endColumns": "76,75,86,90,77,73,76,77,74,72,74,67,72,71,70,73,67,115", "endOffsets": "4511,4587,6823,6914,6992,11290,11367,15031,15106,16221,16296,16852,17147,17367,17508,17900,17968,18154"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\659eba141f87d2afd24e0f28711a233e\\transformed\\play-services-ads-23.6.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "203,242,285,332,392,453,523,583,656,730,821,871,925,993,1055,1087,1132,1162,1192,1226,1267,1299", "endColumns": "38,42,46,59,60,69,59,72,73,90,49,53,67,61,31,44,29,29,33,40,31,55", "endOffsets": "241,284,331,391,452,522,582,655,729,820,870,924,992,1054,1086,1131,1161,1191,1225,1266,1298,1354"}, "to": {"startLines": "207,208,209,212,213,214,215,216,217,218,219,220,221,222,230,231,232,233,234,235,236,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14817,14860,14907,15111,15175,15240,15314,15378,15455,15533,15628,15682,15740,15812,16368,16404,16453,16487,16521,16559,16604,18457", "endColumns": "42,46,50,63,64,73,63,76,77,94,53,57,71,65,35,48,33,33,37,44,35,59", "endOffsets": "14855,14902,14953,15170,15235,15309,15373,15450,15528,15623,15677,15735,15807,15873,16399,16448,16482,16516,16554,16599,16635,18512"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46429bd1dac210616d37d633e605f2e8\\transformed\\browser-1.8.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,139,231,332", "endColumns": "83,91,100,92", "endOffsets": "134,226,327,420"}, "to": {"startLines": "83,143,144,145", "startColumns": "4,4,4,4", "startOffsets": "6483,10411,10503,10604", "endColumns": "83,91,100,92", "endOffsets": "6562,10498,10599,10692"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dc43d647cd71a26bfd564415b69e8be4\\transformed\\play-services-base-18.0.1\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,938,1029,1135,1232,1357,1468,1566,1670,1722,1775", "endColumns": "96,123,110,97,102,111,95,90,105,96,124,110,97,103,51,52,69", "endOffsets": "293,417,528,626,729,841,937,1028,1134,1231,1356,1467,1565,1669,1721,1774,1844"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4664,4765,4893,5008,5110,5217,5333,5433,5631,5741,5842,5971,6086,6188,6296,6352,6409", "endColumns": "100,127,114,101,106,115,99,94,109,100,128,114,101,107,55,56,73", "endOffsets": "4760,4888,5003,5105,5212,5328,5428,5523,5736,5837,5966,6081,6183,6291,6347,6404,6478"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\11b33a2f8f4c91a9de31219b5632fe82\\transformed\\Android-Image-Cropper-4.3.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,177,229,271,328,383,435,485", "endColumns": "74,46,51,41,56,54,51,49,57", "endOffsets": "125,172,224,266,323,378,430,480,538"}, "to": {"startLines": "84,85,86,147,148,149,150,151,224", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6567,6642,6689,10764,10806,10863,10918,10970,15947", "endColumns": "74,46,51,41,56,54,51,49,57", "endOffsets": "6637,6684,6736,10801,10858,10913,10965,11015,16000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c7a5afab22fc7e54eb8c70c357abcc1\\transformed\\react-android-0.76.9-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,193,260,326,401,466,532,602,674,747,822,889,959,1032,1104,1181,1257,1329,1399,1468,1548,1616,1686,1753", "endColumns": "65,71,66,65,74,64,65,69,71,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "116,188,255,321,396,461,527,597,669,742,817,884,954,1027,1099,1176,1252,1324,1394,1463,1543,1611,1681,1748,1817"}, "to": {"startLines": "50,64,146,153,154,158,171,172,173,225,226,229,237,240,241,242,244,245,247,249,250,252,255,257,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3371,4592,10697,11080,11146,11432,12271,12337,12407,16005,16078,16301,16640,16857,16930,17002,17152,17228,17372,17513,17582,17763,17973,18159,18226", "endColumns": "65,71,66,65,74,64,65,69,71,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "3432,4659,10759,11141,11216,11492,12332,12402,12474,16073,16148,16363,16705,16925,16997,17074,17223,17295,17437,17577,17657,17826,18038,18221,18290"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,269,363,470,543,605,683,743,803,881,939,995,1055,1113,1167,1252,1308,1366,1420,1485,1577,1651,1728,1818,1881,1944,2021,2088,2154,2217,2285,2363,2424,2495,2562,2624,2703,2768,2851,2936,3010,3074,3150,3198,3262,3338,3416,3478,3542,3605,3685,3762,3838,3915", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,66,93,106,72,61,77,59,59,77,57,55,59,57,53,84,55,57,53,64,91,73,76,89,62,62,76,66,65,62,67,77,60,70,66,61,78,64,82,84,73,63,75,47,63,75,77,61,63,62,79,76,75,76,68", "endOffsets": "197,264,358,465,538,600,678,738,798,876,934,990,1050,1108,1162,1247,1303,1361,1415,1480,1572,1646,1723,1813,1876,1939,2016,2083,2149,2212,2280,2358,2419,2490,2557,2619,2698,2763,2846,2931,3005,3069,3145,3193,3257,3333,3411,3473,3537,3600,3680,3757,3833,3910,3979"}, "to": {"startLines": "19,51,59,60,61,90,142,152,157,159,160,161,162,163,164,165,166,167,168,169,170,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "658,3437,4165,4259,4366,6997,10333,11020,11372,11497,11575,11633,11689,11749,11807,11861,11946,12002,12060,12114,12179,12479,12553,12630,12720,12783,12846,12923,12990,13056,13119,13187,13265,13326,13397,13464,13526,13605,13670,13753,13838,13912,13976,14052,14100,14164,14240,14318,14380,14444,14507,14587,14664,14740,15878", "endLines": "22,51,59,60,61,90,142,152,157,159,160,161,162,163,164,165,166,167,168,169,170,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,223", "endColumns": "12,66,93,106,72,61,77,59,59,77,57,55,59,57,53,84,55,57,53,64,91,73,76,89,62,62,76,66,65,62,67,77,60,70,66,61,78,64,82,84,73,63,75,47,63,75,77,61,63,62,79,76,75,76,68", "endOffsets": "800,3499,4254,4361,4434,7054,10406,11075,11427,11570,11628,11684,11744,11802,11856,11941,11997,12055,12109,12174,12266,12548,12625,12715,12778,12841,12918,12985,13051,13114,13182,13260,13321,13392,13459,13521,13600,13665,13748,13833,13907,13971,14047,14095,14159,14235,14313,14375,14439,14502,14582,14659,14735,14812,15942"}}]}]}