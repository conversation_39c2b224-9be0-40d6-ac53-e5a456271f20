{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeReleaseResources-79:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dc43d647cd71a26bfd564415b69e8be4\\transformed\\play-services-base-18.0.1\\res\\values-ka\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,439,563,669,819,949,1067,1171,1340,1444,1595,1719,1876,2011,2073,2130", "endColumns": "100,144,123,105,149,129,117,103,168,103,150,123,156,134,61,56,71", "endOffsets": "293,438,562,668,818,948,1066,1170,1339,1443,1594,1718,1875,2010,2072,2129,2201"}, "to": {"startLines": "64,65,66,67,68,69,70,71,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4976,5081,5230,5358,5468,5622,5756,5878,6129,6302,6410,6565,6693,6854,6993,7059,7120", "endColumns": "104,148,127,109,153,133,121,107,172,107,154,127,160,138,65,60,75", "endOffsets": "5076,5225,5353,5463,5617,5751,5873,5981,6297,6405,6560,6688,6849,6988,7054,7115,7191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\659eba141f87d2afd24e0f28711a233e\\transformed\\play-services-ads-23.6.0\\res\\values-ka\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,251,299,353,418,483,586,650,777,889,1005,1058,1119,1235,1326,1369,1446,1480,1515,1569,1647,1690", "endColumns": "51,47,53,64,64,102,63,126,111,115,52,60,115,90,42,76,33,34,53,77,42,55", "endOffsets": "250,298,352,417,482,585,649,776,888,1004,1057,1118,1234,1325,1368,1445,1479,1514,1568,1646,1689,1745"}, "to": {"startLines": "198,199,200,203,204,205,206,207,208,209,210,211,212,213,220,221,222,223,224,225,226,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16430,16486,16538,16773,16842,16911,17018,17086,17217,17333,17453,17510,17575,17695,18286,18333,18414,18452,18491,18549,18631,20649", "endColumns": "55,51,57,68,68,106,67,130,115,119,56,64,119,94,46,80,37,38,57,81,46,59", "endOffsets": "16481,16533,16591,16837,16906,17013,17081,17212,17328,17448,17505,17570,17690,17785,18328,18409,18447,18486,18544,18626,18673,20704"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495d9b066df23cebe365f0b1f0fc7029\\transformed\\exoplayer-ui-2.18.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,290,483,673,757,841,920,1018,1120,1205,1270,1369,1468,1533,1598,1662,1729,1857,1986,2113,2188,2267,2341,2426,2522,2618,2685,2751,2804,2865,2913,2974,3040,3119,3183,3251,3315,3376,3442,3508,3574,3626,3688,3764,3840", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,83,83,78,97,101,84,64,98,98,64,64,63,66,127,128,126,74,78,73,84,95,95,66,65,52,60,47,60,65,78,63,67,63,60,65,65,65,51,61,75,75,52", "endOffsets": "285,478,668,752,836,915,1013,1115,1200,1265,1364,1463,1528,1593,1657,1724,1852,1981,2108,2183,2262,2336,2421,2517,2613,2680,2746,2799,2860,2908,2969,3035,3114,3178,3246,3310,3371,3437,3503,3569,3621,3683,3759,3835,3888"}, "to": {"startLines": "2,11,15,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,340,533,7661,7745,7829,7908,8006,8108,8193,8258,8357,8456,8521,8586,8650,8717,8845,8974,9101,9176,9255,9329,9414,9510,9606,9673,10443,10496,10557,10605,10666,10732,10811,10875,10943,11007,11068,11134,11200,11266,11318,11380,11456,11532", "endLines": "10,14,18,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137", "endColumns": "17,12,12,83,83,78,97,101,84,64,98,98,64,64,63,66,127,128,126,74,78,73,84,95,95,66,65,52,60,47,60,65,78,63,67,63,60,65,65,65,51,61,75,75,52", "endOffsets": "335,528,718,7740,7824,7903,8001,8103,8188,8253,8352,8451,8516,8581,8645,8712,8840,8969,9096,9171,9250,9324,9409,9505,9601,9668,9734,10491,10552,10600,10661,10727,10806,10870,10938,11002,11063,11129,11195,11261,11313,11375,11451,11527,11580"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c7a5afab22fc7e54eb8c70c357abcc1\\transformed\\react-android-0.76.9-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,202,278,368,436,504,581,662,746,826,898,986,1073,1152,1233,1313,1390,1468,1542,1626,1700,1780,1851", "endColumns": "74,71,75,89,67,67,76,80,83,79,71,87,86,78,80,79,76,77,73,83,73,79,70,82", "endOffsets": "125,197,273,363,431,499,576,657,741,821,893,981,1068,1147,1228,1308,1385,1463,1537,1621,1695,1775,1846,1929"}, "to": {"startLines": "50,142,144,145,149,162,163,164,215,216,219,227,230,231,232,234,235,237,239,240,242,245,247,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3599,12000,12141,12217,12546,13501,13569,13646,17870,17954,18214,18678,18920,19007,19086,19245,19325,19480,19633,19707,19892,20115,20316,20387", "endColumns": "74,71,75,89,67,67,76,80,83,79,71,87,86,78,80,79,76,77,73,83,73,79,70,82", "endOffsets": "3669,12067,12212,12302,12609,13564,13641,13722,17949,18029,18281,18761,19002,19081,19162,19320,19397,19553,19702,19786,19961,20190,20382,20465"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,312,411,536,624,691,788,857,920,1007,1073,1133,1202,1263,1317,1432,1491,1551,1605,1677,1807,1895,1979,2087,2165,2241,2335,2402,2468,2541,2619,2705,2778,2856,2934,3009,3099,3174,3268,3366,3440,3517,3617,3670,3738,3827,3916,3978,4043,4106,4213,4311,4411,4510", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,83,98,124,87,66,96,68,62,86,65,59,68,60,53,114,58,59,53,71,129,87,83,107,77,75,93,66,65,72,77,85,72,77,77,74,89,74,93,97,73,76,99,52,67,88,88,61,64,62,106,97,99,98,79", "endOffsets": "223,307,406,531,619,686,783,852,915,1002,1068,1128,1197,1258,1312,1427,1486,1546,1600,1672,1802,1890,1974,2082,2160,2236,2330,2397,2463,2536,2614,2700,2773,2851,2929,3004,3094,3169,3263,3361,3435,3512,3612,3665,3733,3822,3911,3973,4038,4101,4208,4306,4406,4505,4585"}, "to": {"startLines": "19,51,59,60,61,86,138,143,148,150,151,152,153,154,155,156,157,158,159,160,161,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,214", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "723,3674,4482,4581,4706,7594,11585,12072,12483,12614,12701,12767,12827,12896,12957,13011,13126,13185,13245,13299,13371,13727,13815,13899,14007,14085,14161,14255,14322,14388,14461,14539,14625,14698,14776,14854,14929,15019,15094,15188,15286,15360,15437,15537,15590,15658,15747,15836,15898,15963,16026,16133,16231,16331,17790", "endLines": "22,51,59,60,61,86,138,143,148,150,151,152,153,154,155,156,157,158,159,160,161,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,214", "endColumns": "12,83,98,124,87,66,96,68,62,86,65,59,68,60,53,114,58,59,53,71,129,87,83,107,77,75,93,66,65,72,77,85,72,77,77,74,89,74,93,97,73,76,99,52,67,88,88,61,64,62,106,97,99,98,79", "endOffsets": "891,3753,4576,4701,4789,7656,11677,12136,12541,12696,12762,12822,12891,12952,13006,13121,13180,13240,13294,13366,13496,13810,13894,14002,14080,14156,14250,14317,14383,14456,14534,14620,14693,14771,14849,14924,15014,15089,15183,15281,15355,15432,15532,15585,15653,15742,15831,15893,15958,16021,16128,16226,16326,16425,17865"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,228", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "896,1004,1107,1218,1304,1409,1522,1605,1684,1775,1868,1963,2057,2157,2250,2345,2440,2531,2622,2703,2816,2922,3020,3133,3238,3342,3500,18766", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "999,1102,1213,1299,1404,1517,1600,1679,1770,1863,1958,2052,2152,2245,2340,2435,2526,2617,2698,2811,2917,3015,3128,3233,3337,3495,3594,18843"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1c6965b61d3737d12e0f0d1e87d21cde\\transformed\\ui-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,386,489,579,659,755,845,932,1021,1112,1184,1262,1340,1415,1494,1564", "endColumns": "95,85,98,102,89,79,95,89,86,88,90,71,77,77,74,78,69,120", "endOffsets": "196,282,381,484,574,654,750,840,927,1016,1107,1179,1257,1335,1410,1489,1559,1680"}, "to": {"startLines": "62,63,83,84,85,146,147,201,202,217,218,229,233,236,238,243,244,246", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4794,4890,7302,7401,7504,12307,12387,16596,16686,18034,18123,18848,19167,19402,19558,19966,20045,20195", "endColumns": "95,85,98,102,89,79,95,89,86,88,90,71,77,77,74,78,69,120", "endOffsets": "4885,4971,7396,7499,7589,12382,12478,16681,16768,18118,18209,18915,19240,19475,19628,20040,20110,20311"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b56249db71bc4564ba5f782195824c95\\transformed\\exoplayer-core-2.18.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,192,270,343,424,499,587,674", "endColumns": "71,64,77,72,80,74,87,86,84", "endOffsets": "122,187,265,338,419,494,582,669,754"}, "to": {"startLines": "111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9739,9811,9876,9954,10027,10108,10183,10271,10358", "endColumns": "71,64,77,72,80,74,87,86,84", "endOffsets": "9806,9871,9949,10022,10103,10178,10266,10353,10438"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bbb068f5d1da3b4f3e1ef6a35abc59e\\transformed\\foundation-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,90", "endOffsets": "138,229"}, "to": {"startLines": "249,250", "startColumns": "4,4", "startOffsets": "20470,20558", "endColumns": "87,90", "endOffsets": "20553,20644"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "52,53,54,55,56,57,58,241", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3758,3854,3956,4055,4154,4260,4364,19791", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "3849,3951,4050,4149,4255,4359,4477,19887"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0309cb612008fa0a7f120a51a87e98c5\\transformed\\play-services-basement-18.4.0\\res\\values-ka\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "72", "startColumns": "4", "startOffsets": "5986", "endColumns": "142", "endOffsets": "6124"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46429bd1dac210616d37d633e605f2e8\\transformed\\browser-1.8.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,264,374", "endColumns": "105,102,109,104", "endOffsets": "156,259,369,474"}, "to": {"startLines": "82,139,140,141", "startColumns": "4,4,4,4", "startOffsets": "7196,11682,11785,11895", "endColumns": "105,102,109,104", "endOffsets": "7297,11780,11890,11995"}}]}]}