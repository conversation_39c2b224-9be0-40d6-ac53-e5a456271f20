// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { initializeAuth, getReactNativePersistence } from 'firebase/auth'; // Import initializeAuth and persistence
import ReactNativeAsyncStorage from '@react-native-async-storage/async-storage'; // Import AsyncStorage
import { getStorage } from "firebase/storage"; // Import getStorage for Firebase Storage
import { getFirestore } from "firebase/firestore"; // Import getFirestore
import { getFunctions } from "firebase/functions"; // Import getFunctions

// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
export const firebaseConfig = {
  apiKey: "AIzaSyCpYzQOv5Hgz_XtT1Igupl9qdJGjcMYWuY",
  authDomain: "bee-tech-5e03a.firebaseapp.com",
  databaseURL: "https://bee-tech-5e03a-default-rtdb.asia-southeast1.firebasedatabase.app",
  projectId: "bee-tech-5e03a",
  storageBucket: "bee-tech-5e03a.appspot.com", // Corrected storage bucket domain
  messagingSenderId: "189643081632",
  appId: "1:189643081632:web:5dcd41e90221d7914ac6c5"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication with persistence
const auth = initializeAuth(app, {
  persistence: getReactNativePersistence(ReactNativeAsyncStorage)
});

// Initialize Firebase Storage and get a reference to the service
const storage = getStorage(app);

// Initialize Firestore and get a reference to the service
const db = getFirestore(app);

// Initialize Cloud Functions and get a reference to the service
const functions = getFunctions(app);

export { auth, storage, db, functions }; // Export the auth, storage, db, and functions instances
