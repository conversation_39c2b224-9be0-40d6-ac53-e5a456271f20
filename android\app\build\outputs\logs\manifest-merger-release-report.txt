-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:1:1-41:12
INJECTED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:1:1-41:12
INJECTED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:1:1-41:12
INJECTED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:1:1-41:12
MERGED from [:expo] C:\Users\<USER>\quiz-bee-techs\node_modules\expo\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-48:12
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-screens\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-clipboard_clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-clipboard\clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-community_slider] C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-community\slider\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-picker_picker] C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-voice_voice] C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-voice\voice\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-13:12
MERGED from [:react-native-fast-image] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-fast-image\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-haptic-feedback] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-haptic-feedback\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-pdf] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-pdf\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-vector-icons] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-application] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-application\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-asset] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-asset\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-av] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-av\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-18:12
MERGED from [:expo-constants] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-constants\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-client] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-launcher] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-menu] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-device] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-device\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-document-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-20:12
MERGED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-33:12
MERGED from [:expo-font] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-font\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-haptics] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-haptics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:expo-image-loader] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-57:12
MERGED from [:expo-manifests] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-manifests\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-json-utils] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-keep-awake] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-linear-gradient] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-linear-gradient\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-43:12
MERGED from [:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-29:12
MERGED from [:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:expo-splash-screen] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-splash-screen\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-system-ui] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-system-ui\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-menu-interface] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-updates-interface] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-18:12
MERGED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c7a5afab22fc7e54eb8c70c357abcc1\transformed\react-android-0.76.9-release\AndroidManifest.xml:2:1-12:12
MERGED from [com.github.zacharee:AndroidPdfViewer:4.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5eeb06ff5e69f68a9992704711be43d0\transformed\AndroidPdfViewer-4.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.legere:pdfiumandroid:1.0.24] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3b8c0b193a4085f378ec52df877e252\transformed\pdfiumandroid-1.0.24\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab468244bce13502a51b6cbb55b0c3ba\transformed\material-1.6.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e9360051c3ea7eb2bb6965e96006a26\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce5af3863911ead5d9be2a3e850a8df8\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1dc3cf6a41b4f5c2729b5d5e6d5ceec\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:2:1-16:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded2c4e5da925d69ac878333fc674af8\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:2:1-38:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9f07af94ab12324b088a056fa5fe92a\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d069f7b29a00acab42a1886db7c7e8\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ef81723d7a2bbea1faa708338cfccef\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42358a9cae14349299c1c7a268311762\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-ads:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\659eba141f87d2afd24e0f28711a233e\transformed\play-services-ads-23.6.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:17:1-112:12
MERGED from [com.google.android.ump:user-messaging-platform:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce48b4faec6a27f98c892ad1f9c6d0c1\transformed\user-messaging-platform-3.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.gms:play-services-ads-base:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a56c650de9e510c33e21f2818f4e935b\transformed\play-services-ads-base-23.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e9e926ac31179e2d2f965aef6a4ce9d\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c00fa954801a0b63f420de7e4f0ec5a\transformed\play-services-appset-16.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91477e5d6c9216b70ac507e0b5e1f66e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a36ef59153d76ba43c2451c2df84718d\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc43d647cd71a26bfd564415b69e8be4\transformed\play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3a7ef0c6947795167b7587418640207\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77adb90f0d2b1e86d9ed6ead521b7e6b\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bbb068f5d1da3b4f3e1ef6a35abc59e\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56eb825ab107656304755aa094183eea\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\81d5df2ceb727ebe84e7e21c8079732c\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4939e953aae0a27b9ce80589bad802b\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59afbf8b23e888be6f1c7734ba623e76\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\064d0148c56b6c5d5ef1b287b26d47ef\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10070b27062eead2e3cb49aca4540174\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da8537b083b8d19e70741f4ea0f38cd5\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df09627d4dca6349b08b54926ebafa65\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c6965b61d3737d12e0f0d1e87d21cde\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\412b8eb328b41a1b88da9da9253a90c7\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0dd6eb261391cf90a366b6b53c7972ec\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\533a8641643c7455d80750d5b1d69574\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f2f6d555522cf6406d2730284c437b3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4758c8a4eea773d09a4224a099318e94\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bd07eeb7db9c3f364e0665aebff3c2e\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867f28603a9adbd922e925087114cbc1\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4c121cc1fa925fa3ea9e507c876dc3c\transformed\activity-ktx-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac92a391416880e47e753b1895b49d58\transformed\activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c912dc50b3d9447b40190b74c93563bd\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7773267b7755c5291a13dcd14f714ab6\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec9c8e3b41151a01d535f4b979198110\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:animated-gif:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b3c27b8715ccbf12f39fc3e95c5e4bd\transformed\animated-gif-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8c6164221c34271a60de2fae779c229\transformed\webpsupport-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4a33703994ab140c843338d56da8951\transformed\fresco-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2be96e8b87d277b854ec8c5007bdcac0\transformed\imagepipeline-okhttp3-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4458a7074ae487c8ace58e625437735\transformed\animated-base-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dc1d7d8f964947d0fcf350d082b35c4\transformed\animated-drawable-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3cf45652d3d887ae44135c4a1f72e42e\transformed\vito-options-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a24b42100d57a50c003dd7605f9b1482\transformed\drawee-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fed065f1f35e08884615e144948f0c86\transformed\nativeimagefilters-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c530c2889471fbb98cf57c344c6bfc7\transformed\memory-type-native-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e24bd0f285e718792a40463891b1e7f5\transformed\memory-type-java-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\882cf36150d4788733effd0de347876a\transformed\imagepipeline-native-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8a030a0a1c869be0de86c28b012b5fe\transformed\memory-type-ashmem-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ec33e27a9818d163e5899ba16b3ebc8\transformed\imagepipeline-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a67a835b59f1f177f880c32d2a32ab32\transformed\nativeimagetranscoder-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\949bfb069bb86a27c60b1bf28de9f7f0\transformed\imagepipeline-base-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc1770dab171783bbeb7adc8fc0673d8\transformed\middleware-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6a4fd1f2fcdd5e65092f286eeeb092c\transformed\ui-common-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4d8cd653f2b0663ebbeb858a7981dcf\transformed\soloader-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9a6e860c564bc8b3abeeba78e2b23d8\transformed\fbcore-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d7f5c2c9c6511deaf48ddd1c8ce237\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa2d5bf76eb464c8ff4defe2fea1aac0\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb78cb2606b59601acb842c8ab3e103a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\082451452290844d49c7753666f3fee2\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46429bd1dac210616d37d633e605f2e8\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb95df6e8dc87d96e5b90ad54cc2f639\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\761bf0a013907914625323f6c5f5e375\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dd6b5959c290340ee2e59db550ce6ac\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\309fd560825ae2d8ef85f2cfb55064cc\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5400ddf941a1708e47523555736a486\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5054ca3c6a93ab2c38873707e6cc5add\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4177a993f44099c31938b0bb41321ac7\transformed\exoplayer-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e83de037299078ff8358e32a65146e8b\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd14dd586561d345d46c048b6f5d2789\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c53bd2d021e577017c0ea4f07f861391\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7e128b3346c61d7809e422d8b74fedf\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b56249db71bc4564ba5f782195824c95\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56cbabaf7bb587fc1188fe01ec91462d\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\495d9b066df23cebe365f0b1f0fc7029\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\744bf7af77a331f13c23a29495fde568\transformed\recyclerview-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1731c9ae57ec2027ac0776783af3599d\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f2e302e5b8940d71f26b4d2c2d57c11\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd4778664200ef8409b2356b390b4dcc\transformed\media-1.4.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5dc663e86168e3f68d9a2197ed4b4ef\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cea7659faddaea601dd885c59c277f88\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3efba9121930f5266df32052f7ce7978\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91072b9116df2d09980cdb4b7797bc04\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8be5c91f3fcc34441c2b7b5edc7aeb8f\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c6e6ae314084bbd5cb8ff5d2cb4fb4c\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4da8ca71cd23b22c5245dbaee097957b\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c70d98d12325b882d76b89aa8b1d57ba\transformed\lifecycle-runtime-2.8.3\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4436c2945a3322575c204d604927d67d\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f044b3b59b5572416eeba5380e87b66\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5bf25326533eb29ec284fbce91a14aec\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9cc87a2f034f79d25e1d72044cb8fc8e\transformed\lifecycle-service-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad10a3280ed9ff2393dd52d1764f5de6\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\106ab7c22a3718bedfecd752dd0d68a0\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\493db4f0ca2f3a31bc58a129514182f8\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b43c6b9e18fd8d65c4c9caca4e493b\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca2c75000a3dc2dc2c37a1d3a8bfa22\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05d5543dc97d3bd73ecdc361da3204eb\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bb6704efd91be37f1e08416cfe4e79c\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd09a11fb26e570c82f35f0289ac74e0\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb1a86035874716827a6fc2c6b8992ae\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f97925ebbd6fa09527b11375bc8e717\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0b876c1b7f5119d8d465be5cc7850ce\transformed\play-services-measurement-base-20.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0309cb612008fa0a7f120a51a87e98c5\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2083bf72e6b404572a732073264ff144\transformed\fragment-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b0d1513c313fd2f1285485128d1180e\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\720f8bfa2f89870d854f88b902b470f8\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b519326dc06e70070242a413ed557da\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3cb5d21b8c3286331b3e06cfcd4be8df\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f102f5f2af0f9920a4ec2bbb16286a0\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.react:hermes-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\daebce6d293e79ddda89f722b2e71ba9\transformed\hermes-android-0.76.9-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32e32c61d7a8344f78f9e5ecbc1705ec\transformed\viewbinding-8.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d69efae93c03c835a3b5c84b11a1ec\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97471c397bccf6da42a783affda4a2d9\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1b3536639a988ae7b5cfd911455628f\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb09bf49364a9fe47e8dca077ca1d888\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ab96a97679138b4468581ca13933152\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c68e127b7ada1b8186bfd581f97604a3\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\602e46738587ab61710877f6243c4b37\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8db616d994193aab5184ff3b00afbda\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52945983a2ba3c3e4198eb0a05864b2e\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\820c4f1e44229fb85aad13a17e92db82\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10cf65477e02ebee6ddd7715742cb3aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63b29f9e1063f3908dbbea59d82125c9\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de30b92ed9545aab089d3695f4f8c6bb\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58419a84b68c48343c7ffd5f50a82b6b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01c68b7f99bce65bd3d6563c2b710b81\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d01a4295d9ec2fa124ca5810346eeaa\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f204f19a11d698f91d4acbb23137b35e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\667cfa51b38ae60fa93671246cb6081b\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc0bd38c684e6b7e213282ff4b2616bd\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe35776a6b655080e9af84e104a8e037\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e73ff42a7da05e4a493c8db2334e2b9\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:vito-renderer:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\04f85e42967ab1fce55f7b61ebffddb3\transformed\vito-renderer-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07bf45f3c2473093a4271de984b194a6\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.android.support:appcompat-v7:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf558a140f261b5d67b87ced93e193c2\transformed\appcompat-v7-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fbjni:fbjni:0.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1957b0db02baa50e26af4d5b92e700f6\transformed\fbjni-0.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\94e6775a19cca0cd325a9f408af6a2dc\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
MERGED from [com.android.support:support-fragment:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4486def0a1ddb0a16b14b527fdfb1f21\transformed\support-fragment-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:animated-vector-drawable:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed7d39d8e0aceb01c08b7bdd080b1661\transformed\animated-vector-drawable-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-core-ui:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fb9960c574b86629d1121e8e6eaf3cf\transformed\support-core-ui-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-core-utils:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83cec9bf728b2ea76114bd8ce62fb21e\transformed\support-core-utils-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-vector-drawable:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bdf20d9fb3bc2986652116bbbce6b7b\transformed\support-vector-drawable-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:loader:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a08d3be46356e97a4d38925042bb243\transformed\loader-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:viewpager:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\663ebbf54021bccc3bfe273e41cf299d\transformed\viewpager-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:coordinatorlayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bec8f7f3dc33ba79ba8bfc9c468757f3\transformed\coordinatorlayout-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:drawerlayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\819b84d1fbf659aba3e29e4ab7917b57\transformed\drawerlayout-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:slidingpanelayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d75aa8b6dd6f87e0b37baa5cb92e175\transformed\slidingpanelayout-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:customview:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4286670191318f1fd72103a5cb8bab3\transformed\customview-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:swiperefreshlayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d140497454d262e7ae7fbe99b3f614a4\transformed\swiperefreshlayout-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:asynclayoutinflater:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f1047bec2525d4e024f6dd76efef1de\transformed\asynclayoutinflater-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-compat:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b1f7ff49c8203eedd2425ac0bd53f5f\transformed\support-compat-28.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.android.support:versionedparcelable:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52188eee32d86784bab2f47543e011b7\transformed\versionedparcelable-28.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.android.support:cursoradapter:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42c837396384e45981cd41b8d128084f\transformed\cursoradapter-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [android.arch.lifecycle:runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45850e6f0701a9f7c52949eb8f93999b\transformed\runtime-1.1.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:documentfile:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3053c3395366edfec0592d439cc4594\transformed\documentfile-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:localbroadcastmanager:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23b0318bf3288b448001701333dd7ba9\transformed\localbroadcastmanager-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:print:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b5eb9e94ad725a09e96f0e81c7352d3\transformed\print-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [android.arch.lifecycle:viewmodel:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ba350c82b81d9c855bdcc34a6789626\transformed\viewmodel-1.1.1\AndroidManifest.xml:17:1-22:12
MERGED from [android.arch.lifecycle:livedata:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\257cf0f385254b513fb58820af5da348\transformed\livedata-1.1.1\AndroidManifest.xml:17:1-22:12
MERGED from [android.arch.lifecycle:livedata-core:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d19feff7181683dbab384d2a50904831\transformed\livedata-core-1.1.1\AndroidManifest.xml:17:1-22:12
MERGED from [android.arch.core:runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f259b13cb15c268d5bf7f4414c629cb0\transformed\runtime-1.1.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:interpolator:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7666d55386ce438417c89eb9eacc22d\transformed\interpolator-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65e83cd3aee2760cef02a821b313c704\transformed\installreferrer-2.2\AndroidManifest.xml:2:1-13:12
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:2:1-52:12
	package
		INJECTED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:1:70-116
	android:versionCode
		INJECTED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:2:3-62
MERGED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-65
MERGED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-65
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:2:20-60
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:3:3-64
MERGED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-67
MERGED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-67
MERGED from [:react-native-voice_voice] C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-voice\voice\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:5-67
MERGED from [:react-native-voice_voice] C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-voice\voice\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:5-67
MERGED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-67
MERGED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3a7ef0c6947795167b7587418640207\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3a7ef0c6947795167b7587418640207\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f97925ebbd6fa09527b11375bc8e717\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f97925ebbd6fa09527b11375bc8e717\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:3:20-62
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:4:3-77
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:4:20-75
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:5:3-77
MERGED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-80
MERGED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-80
MERGED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:5-80
MERGED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:5-80
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:5:20-75
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:6:3-68
MERGED from [:react-native-voice_voice] C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-voice\voice\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-71
MERGED from [:react-native-voice_voice] C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-voice\voice\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-71
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:6:20-66
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:7:3-75
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:7:20-73
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:8:3-63
MERGED from [:react-native-haptic-feedback] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-haptic-feedback\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-66
MERGED from [:react-native-haptic-feedback] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-haptic-feedback\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-66
MERGED from [:expo-haptics] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-haptics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-66
MERGED from [:expo-haptics] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-haptics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-66
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:8:20-61
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:9:3-65
MERGED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-68
MERGED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-68
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3a7ef0c6947795167b7587418640207\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3a7ef0c6947795167b7587418640207\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f97925ebbd6fa09527b11375bc8e717\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f97925ebbd6fa09527b11375bc8e717\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:9:20-63
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:10:3-78
MERGED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-81
MERGED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-81
MERGED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:5-81
MERGED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:5-81
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:10:20-76
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:11:3-76
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e9e926ac31179e2d2f965aef6a4ce9d\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e9e926ac31179e2d2f965aef6a4ce9d\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f97925ebbd6fa09527b11375bc8e717\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f97925ebbd6fa09527b11375bc8e717\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:26:5-79
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:11:20-74
queries
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:12:3-18:13
MERGED from [:expo-document-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-18:15
MERGED from [:expo-document-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-18:15
MERGED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:5-18:15
MERGED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:5-18:15
MERGED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:5-25:15
MERGED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:5-25:15
MERGED from [:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-15:15
MERGED from [:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-15:15
MERGED from [:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-13:15
MERGED from [:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-13:15
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:9:5-20:15
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:9:5-20:15
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:35:5-68:15
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:35:5-68:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:13:5-17:14
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:7-58
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:15-56
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-67
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:17-65
data
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
	android:scheme
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:13-35
application
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:3-40:17
INJECTED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:3-40:17
MERGED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:5-46:19
MERGED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:5-46:19
MERGED from [:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-16:19
MERGED from [:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-16:19
MERGED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:5-55:19
MERGED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:5-55:19
MERGED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-41:19
MERGED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-41:19
MERGED from [:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:5-27:19
MERGED from [:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:5-27:19
MERGED from [:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab468244bce13502a51b6cbb55b0c3ba\transformed\material-1.6.1\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab468244bce13502a51b6cbb55b0c3ba\transformed\material-1.6.1\AndroidManifest.xml:24:5-20
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e9360051c3ea7eb2bb6965e96006a26\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e9360051c3ea7eb2bb6965e96006a26\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1dc3cf6a41b4f5c2729b5d5e6d5ceec\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:10:5-14:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1dc3cf6a41b4f5c2729b5d5e6d5ceec\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:10:5-14:19
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:22:5-36:19
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:22:5-36:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9f07af94ab12324b088a056fa5fe92a\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9f07af94ab12324b088a056fa5fe92a\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:70:5-110:19
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:70:5-110:19
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.gms:play-services-ads-base:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a56c650de9e510c33e21f2818f4e935b\transformed\play-services-ads-base-23.6.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-base:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a56c650de9e510c33e21f2818f4e935b\transformed\play-services-ads-base-23.6.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e9e926ac31179e2d2f965aef6a4ce9d\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e9e926ac31179e2d2f965aef6a4ce9d\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c00fa954801a0b63f420de7e4f0ec5a\transformed\play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c00fa954801a0b63f420de7e4f0ec5a\transformed\play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91477e5d6c9216b70ac507e0b5e1f66e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91477e5d6c9216b70ac507e0b5e1f66e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a36ef59153d76ba43c2451c2df84718d\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a36ef59153d76ba43c2451c2df84718d\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc43d647cd71a26bfd564415b69e8be4\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc43d647cd71a26bfd564415b69e8be4\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\533a8641643c7455d80750d5b1d69574\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\533a8641643c7455d80750d5b1d69574\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867f28603a9adbd922e925087114cbc1\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867f28603a9adbd922e925087114cbc1\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa2d5bf76eb464c8ff4defe2fea1aac0\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa2d5bf76eb464c8ff4defe2fea1aac0\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad10a3280ed9ff2393dd52d1764f5de6\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad10a3280ed9ff2393dd52d1764f5de6\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd09a11fb26e570c82f35f0289ac74e0\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd09a11fb26e570c82f35f0289ac74e0\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb1a86035874716827a6fc2c6b8992ae\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb1a86035874716827a6fc2c6b8992ae\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0b876c1b7f5119d8d465be5cc7850ce\transformed\play-services-measurement-base-20.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0b876c1b7f5119d8d465be5cc7850ce\transformed\play-services-measurement-base-20.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0309cb612008fa0a7f120a51a87e98c5\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0309cb612008fa0a7f120a51a87e98c5\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b519326dc06e70070242a413ed557da\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b519326dc06e70070242a413ed557da\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10cf65477e02ebee6ddd7715742cb3aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10cf65477e02ebee6ddd7715742cb3aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01c68b7f99bce65bd3d6563c2b710b81\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01c68b7f99bce65bd3d6563c2b710b81\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\94e6775a19cca0cd325a9f408af6a2dc\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\94e6775a19cca0cd325a9f408af6a2dc\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.android.support:support-compat:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b1f7ff49c8203eedd2425ac0bd53f5f\transformed\support-compat-28.0.0\AndroidManifest.xml:22:5-94
MERGED from [com.android.support:support-compat:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b1f7ff49c8203eedd2425ac0bd53f5f\transformed\support-compat-28.0.0\AndroidManifest.xml:22:5-94
MERGED from [com.android.support:versionedparcelable:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52188eee32d86784bab2f47543e011b7\transformed\versionedparcelable-28.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.android.support:versionedparcelable:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52188eee32d86784bab2f47543e011b7\transformed\versionedparcelable-28.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65e83cd3aee2760cef02a821b313c704\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65e83cd3aee2760cef02a821b313c704\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml
	android:supportsRtl
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:221-247
	android:label
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:48-80
	android:appComponentFactory
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:248-316
		REJECTED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
		REJECTED from [com.android.support:support-compat:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b1f7ff49c8203eedd2425ac0bd53f5f\transformed\support-compat-28.0.0\AndroidManifest.xml:22:18-91
	android:roundIcon
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:116-161
	android:icon
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:81-115
	android:allowBackup
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:162-188
	android:theme
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:189-220
	tools:replace
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:317-360
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:16-47
meta-data#com.google.android.gms.ads.APPLICATION_ID
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:5-159
MERGED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:9-15:32
MERGED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:9-15:32
	tools:replace
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:128-157
	android:value
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:73-127
		REJECTED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-29
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:16-72
meta-data#com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:21:5-137
MERGED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:9-18:37
MERGED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:9-18:37
	tools:replace
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:21:106-135
	android:value
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:21:85-105
		REJECTED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-34
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:21:16-84
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:22:5-135
MERGED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:9-24:36
MERGED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:9-24:36
	tools:replace
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:22:104-133
	android:value
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:22:83-103
		REJECTED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-33
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:22:16-82
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:5-139
MERGED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-21:36
MERGED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-21:36
	tools:replace
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:108-137
	android:value
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:87-107
		REJECTED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-33
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:16-86
meta-data#expo.modules.updates.ENABLED
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:5-83
	android:value
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:60-81
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:16-59
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:25:5-105
	android:value
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:25:81-103
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:25:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:26:5-99
	android:value
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:26:80-97
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:26:16-79
activity#com.gokul719.snack97152fc1f368437dac54171df4ba22bd.MainActivity
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:5-39:16
	android:screenOrientation
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:280-316
	android:launchMode
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:135-166
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:167-209
	android:exported
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:256-279
	android:configChanges
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:44-134
	android:theme
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:210-255
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:28:7-31:23
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:29:9-60
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:29:17-58
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:30:9-68
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:30:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:com.gokul719.snack97152fc1f368437dac54171df4ba22bd+data:scheme:exp+snack-97152fc1-f368-437d-ac54-171df4ba22bd
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:32:7-38:23
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:34:9-67
	android:name
		ADDED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:34:19-65
uses-sdk
INJECTED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml
MERGED from [:expo] C:\Users\<USER>\quiz-bee-techs\node_modules\expo\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] C:\Users\<USER>\quiz-bee-techs\node_modules\expo\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-screens\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-screens\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-clipboard_clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-clipboard\clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-clipboard_clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-clipboard\clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_slider] C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-community\slider\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_slider] C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-community\slider\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-picker_picker] C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-picker_picker] C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-voice_voice] C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-voice\voice\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-8:54
MERGED from [:react-native-voice_voice] C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-voice\voice\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-8:54
MERGED from [:react-native-fast-image] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-fast-image\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-fast-image] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-fast-image\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-haptic-feedback] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-haptic-feedback\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-haptic-feedback] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-haptic-feedback\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-pdf] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-pdf\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-pdf] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-pdf\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-application] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-application\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-application] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-application\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-asset] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-asset\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-asset] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-asset\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-av] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-av\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-av] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-av\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-constants\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-constants\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-device] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-device\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-device] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-device\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-document-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-document-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-font] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-font\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-font] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-font\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-haptics] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-haptics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-haptics] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-haptics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-loader] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-loader] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-manifests] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-manifests\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-manifests\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-keep-awake] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-keep-awake] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-linear-gradient] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-linear-gradient\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-linear-gradient] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-linear-gradient\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-splash-screen] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-splash-screen\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-splash-screen] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-splash-screen\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-system-ui] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-system-ui\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-system-ui] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-system-ui\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c7a5afab22fc7e54eb8c70c357abcc1\transformed\react-android-0.76.9-release\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c7a5afab22fc7e54eb8c70c357abcc1\transformed\react-android-0.76.9-release\AndroidManifest.xml:10:5-44
MERGED from [com.github.zacharee:AndroidPdfViewer:4.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5eeb06ff5e69f68a9992704711be43d0\transformed\AndroidPdfViewer-4.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.zacharee:AndroidPdfViewer:4.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5eeb06ff5e69f68a9992704711be43d0\transformed\AndroidPdfViewer-4.0.1\AndroidManifest.xml:5:5-44
MERGED from [io.legere:pdfiumandroid:1.0.24] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3b8c0b193a4085f378ec52df877e252\transformed\pdfiumandroid-1.0.24\AndroidManifest.xml:5:5-44
MERGED from [io.legere:pdfiumandroid:1.0.24] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3b8c0b193a4085f378ec52df877e252\transformed\pdfiumandroid-1.0.24\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab468244bce13502a51b6cbb55b0c3ba\transformed\material-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab468244bce13502a51b6cbb55b0c3ba\transformed\material-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e9360051c3ea7eb2bb6965e96006a26\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e9360051c3ea7eb2bb6965e96006a26\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce5af3863911ead5d9be2a3e850a8df8\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce5af3863911ead5d9be2a3e850a8df8\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1dc3cf6a41b4f5c2729b5d5e6d5ceec\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1dc3cf6a41b4f5c2729b5d5e6d5ceec\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded2c4e5da925d69ac878333fc674af8\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded2c4e5da925d69ac878333fc674af8\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9f07af94ab12324b088a056fa5fe92a\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9f07af94ab12324b088a056fa5fe92a\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d069f7b29a00acab42a1886db7c7e8\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d069f7b29a00acab42a1886db7c7e8\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ef81723d7a2bbea1faa708338cfccef\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ef81723d7a2bbea1faa708338cfccef\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42358a9cae14349299c1c7a268311762\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42358a9cae14349299c1c7a268311762\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\659eba141f87d2afd24e0f28711a233e\transformed\play-services-ads-23.6.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\659eba141f87d2afd24e0f28711a233e\transformed\play-services-ads-23.6.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce48b4faec6a27f98c892ad1f9c6d0c1\transformed\user-messaging-platform-3.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce48b4faec6a27f98c892ad1f9c6d0c1\transformed\user-messaging-platform-3.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-base:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a56c650de9e510c33e21f2818f4e935b\transformed\play-services-ads-base-23.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-base:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a56c650de9e510c33e21f2818f4e935b\transformed\play-services-ads-base-23.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e9e926ac31179e2d2f965aef6a4ce9d\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e9e926ac31179e2d2f965aef6a4ce9d\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c00fa954801a0b63f420de7e4f0ec5a\transformed\play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c00fa954801a0b63f420de7e4f0ec5a\transformed\play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91477e5d6c9216b70ac507e0b5e1f66e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91477e5d6c9216b70ac507e0b5e1f66e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a36ef59153d76ba43c2451c2df84718d\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a36ef59153d76ba43c2451c2df84718d\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc43d647cd71a26bfd564415b69e8be4\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc43d647cd71a26bfd564415b69e8be4\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3a7ef0c6947795167b7587418640207\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3a7ef0c6947795167b7587418640207\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77adb90f0d2b1e86d9ed6ead521b7e6b\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77adb90f0d2b1e86d9ed6ead521b7e6b\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bbb068f5d1da3b4f3e1ef6a35abc59e\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bbb068f5d1da3b4f3e1ef6a35abc59e\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56eb825ab107656304755aa094183eea\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56eb825ab107656304755aa094183eea\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\81d5df2ceb727ebe84e7e21c8079732c\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\81d5df2ceb727ebe84e7e21c8079732c\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4939e953aae0a27b9ce80589bad802b\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4939e953aae0a27b9ce80589bad802b\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59afbf8b23e888be6f1c7734ba623e76\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59afbf8b23e888be6f1c7734ba623e76\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\064d0148c56b6c5d5ef1b287b26d47ef\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\064d0148c56b6c5d5ef1b287b26d47ef\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10070b27062eead2e3cb49aca4540174\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10070b27062eead2e3cb49aca4540174\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da8537b083b8d19e70741f4ea0f38cd5\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da8537b083b8d19e70741f4ea0f38cd5\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df09627d4dca6349b08b54926ebafa65\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df09627d4dca6349b08b54926ebafa65\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c6965b61d3737d12e0f0d1e87d21cde\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c6965b61d3737d12e0f0d1e87d21cde\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\412b8eb328b41a1b88da9da9253a90c7\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\412b8eb328b41a1b88da9da9253a90c7\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0dd6eb261391cf90a366b6b53c7972ec\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0dd6eb261391cf90a366b6b53c7972ec\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\533a8641643c7455d80750d5b1d69574\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\533a8641643c7455d80750d5b1d69574\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f2f6d555522cf6406d2730284c437b3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f2f6d555522cf6406d2730284c437b3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4758c8a4eea773d09a4224a099318e94\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4758c8a4eea773d09a4224a099318e94\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bd07eeb7db9c3f364e0665aebff3c2e\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bd07eeb7db9c3f364e0665aebff3c2e\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867f28603a9adbd922e925087114cbc1\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867f28603a9adbd922e925087114cbc1\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4c121cc1fa925fa3ea9e507c876dc3c\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4c121cc1fa925fa3ea9e507c876dc3c\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac92a391416880e47e753b1895b49d58\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac92a391416880e47e753b1895b49d58\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c912dc50b3d9447b40190b74c93563bd\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c912dc50b3d9447b40190b74c93563bd\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7773267b7755c5291a13dcd14f714ab6\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7773267b7755c5291a13dcd14f714ab6\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec9c8e3b41151a01d535f4b979198110\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec9c8e3b41151a01d535f4b979198110\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:animated-gif:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b3c27b8715ccbf12f39fc3e95c5e4bd\transformed\animated-gif-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b3c27b8715ccbf12f39fc3e95c5e4bd\transformed\animated-gif-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8c6164221c34271a60de2fae779c229\transformed\webpsupport-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8c6164221c34271a60de2fae779c229\transformed\webpsupport-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4a33703994ab140c843338d56da8951\transformed\fresco-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4a33703994ab140c843338d56da8951\transformed\fresco-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2be96e8b87d277b854ec8c5007bdcac0\transformed\imagepipeline-okhttp3-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2be96e8b87d277b854ec8c5007bdcac0\transformed\imagepipeline-okhttp3-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4458a7074ae487c8ace58e625437735\transformed\animated-base-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4458a7074ae487c8ace58e625437735\transformed\animated-base-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dc1d7d8f964947d0fcf350d082b35c4\transformed\animated-drawable-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dc1d7d8f964947d0fcf350d082b35c4\transformed\animated-drawable-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3cf45652d3d887ae44135c4a1f72e42e\transformed\vito-options-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3cf45652d3d887ae44135c4a1f72e42e\transformed\vito-options-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a24b42100d57a50c003dd7605f9b1482\transformed\drawee-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a24b42100d57a50c003dd7605f9b1482\transformed\drawee-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fed065f1f35e08884615e144948f0c86\transformed\nativeimagefilters-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fed065f1f35e08884615e144948f0c86\transformed\nativeimagefilters-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c530c2889471fbb98cf57c344c6bfc7\transformed\memory-type-native-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c530c2889471fbb98cf57c344c6bfc7\transformed\memory-type-native-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e24bd0f285e718792a40463891b1e7f5\transformed\memory-type-java-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e24bd0f285e718792a40463891b1e7f5\transformed\memory-type-java-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\882cf36150d4788733effd0de347876a\transformed\imagepipeline-native-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\882cf36150d4788733effd0de347876a\transformed\imagepipeline-native-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8a030a0a1c869be0de86c28b012b5fe\transformed\memory-type-ashmem-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8a030a0a1c869be0de86c28b012b5fe\transformed\memory-type-ashmem-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ec33e27a9818d163e5899ba16b3ebc8\transformed\imagepipeline-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ec33e27a9818d163e5899ba16b3ebc8\transformed\imagepipeline-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a67a835b59f1f177f880c32d2a32ab32\transformed\nativeimagetranscoder-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a67a835b59f1f177f880c32d2a32ab32\transformed\nativeimagetranscoder-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\949bfb069bb86a27c60b1bf28de9f7f0\transformed\imagepipeline-base-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\949bfb069bb86a27c60b1bf28de9f7f0\transformed\imagepipeline-base-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc1770dab171783bbeb7adc8fc0673d8\transformed\middleware-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc1770dab171783bbeb7adc8fc0673d8\transformed\middleware-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6a4fd1f2fcdd5e65092f286eeeb092c\transformed\ui-common-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6a4fd1f2fcdd5e65092f286eeeb092c\transformed\ui-common-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4d8cd653f2b0663ebbeb858a7981dcf\transformed\soloader-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4d8cd653f2b0663ebbeb858a7981dcf\transformed\soloader-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9a6e860c564bc8b3abeeba78e2b23d8\transformed\fbcore-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9a6e860c564bc8b3abeeba78e2b23d8\transformed\fbcore-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d7f5c2c9c6511deaf48ddd1c8ce237\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d7f5c2c9c6511deaf48ddd1c8ce237\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa2d5bf76eb464c8ff4defe2fea1aac0\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa2d5bf76eb464c8ff4defe2fea1aac0\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb78cb2606b59601acb842c8ab3e103a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb78cb2606b59601acb842c8ab3e103a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\082451452290844d49c7753666f3fee2\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\082451452290844d49c7753666f3fee2\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46429bd1dac210616d37d633e605f2e8\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46429bd1dac210616d37d633e605f2e8\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb95df6e8dc87d96e5b90ad54cc2f639\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb95df6e8dc87d96e5b90ad54cc2f639\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\761bf0a013907914625323f6c5f5e375\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\761bf0a013907914625323f6c5f5e375\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dd6b5959c290340ee2e59db550ce6ac\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dd6b5959c290340ee2e59db550ce6ac\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\309fd560825ae2d8ef85f2cfb55064cc\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\309fd560825ae2d8ef85f2cfb55064cc\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5400ddf941a1708e47523555736a486\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5400ddf941a1708e47523555736a486\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5054ca3c6a93ab2c38873707e6cc5add\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5054ca3c6a93ab2c38873707e6cc5add\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4177a993f44099c31938b0bb41321ac7\transformed\exoplayer-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4177a993f44099c31938b0bb41321ac7\transformed\exoplayer-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e83de037299078ff8358e32a65146e8b\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e83de037299078ff8358e32a65146e8b\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd14dd586561d345d46c048b6f5d2789\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd14dd586561d345d46c048b6f5d2789\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c53bd2d021e577017c0ea4f07f861391\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c53bd2d021e577017c0ea4f07f861391\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7e128b3346c61d7809e422d8b74fedf\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7e128b3346c61d7809e422d8b74fedf\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b56249db71bc4564ba5f782195824c95\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b56249db71bc4564ba5f782195824c95\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56cbabaf7bb587fc1188fe01ec91462d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56cbabaf7bb587fc1188fe01ec91462d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\495d9b066df23cebe365f0b1f0fc7029\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\495d9b066df23cebe365f0b1f0fc7029\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\744bf7af77a331f13c23a29495fde568\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\744bf7af77a331f13c23a29495fde568\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1731c9ae57ec2027ac0776783af3599d\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1731c9ae57ec2027ac0776783af3599d\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f2e302e5b8940d71f26b4d2c2d57c11\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f2e302e5b8940d71f26b4d2c2d57c11\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd4778664200ef8409b2356b390b4dcc\transformed\media-1.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd4778664200ef8409b2356b390b4dcc\transformed\media-1.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5dc663e86168e3f68d9a2197ed4b4ef\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5dc663e86168e3f68d9a2197ed4b4ef\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cea7659faddaea601dd885c59c277f88\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cea7659faddaea601dd885c59c277f88\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3efba9121930f5266df32052f7ce7978\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3efba9121930f5266df32052f7ce7978\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91072b9116df2d09980cdb4b7797bc04\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91072b9116df2d09980cdb4b7797bc04\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8be5c91f3fcc34441c2b7b5edc7aeb8f\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8be5c91f3fcc34441c2b7b5edc7aeb8f\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c6e6ae314084bbd5cb8ff5d2cb4fb4c\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c6e6ae314084bbd5cb8ff5d2cb4fb4c\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4da8ca71cd23b22c5245dbaee097957b\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4da8ca71cd23b22c5245dbaee097957b\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c70d98d12325b882d76b89aa8b1d57ba\transformed\lifecycle-runtime-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-runtime:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c70d98d12325b882d76b89aa8b1d57ba\transformed\lifecycle-runtime-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4436c2945a3322575c204d604927d67d\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4436c2945a3322575c204d604927d67d\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f044b3b59b5572416eeba5380e87b66\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f044b3b59b5572416eeba5380e87b66\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5bf25326533eb29ec284fbce91a14aec\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5bf25326533eb29ec284fbce91a14aec\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9cc87a2f034f79d25e1d72044cb8fc8e\transformed\lifecycle-service-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9cc87a2f034f79d25e1d72044cb8fc8e\transformed\lifecycle-service-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad10a3280ed9ff2393dd52d1764f5de6\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad10a3280ed9ff2393dd52d1764f5de6\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\106ab7c22a3718bedfecd752dd0d68a0\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\106ab7c22a3718bedfecd752dd0d68a0\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\493db4f0ca2f3a31bc58a129514182f8\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\493db4f0ca2f3a31bc58a129514182f8\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b43c6b9e18fd8d65c4c9caca4e493b\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b43c6b9e18fd8d65c4c9caca4e493b\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca2c75000a3dc2dc2c37a1d3a8bfa22\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca2c75000a3dc2dc2c37a1d3a8bfa22\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05d5543dc97d3bd73ecdc361da3204eb\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05d5543dc97d3bd73ecdc361da3204eb\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bb6704efd91be37f1e08416cfe4e79c\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bb6704efd91be37f1e08416cfe4e79c\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd09a11fb26e570c82f35f0289ac74e0\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd09a11fb26e570c82f35f0289ac74e0\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb1a86035874716827a6fc2c6b8992ae\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb1a86035874716827a6fc2c6b8992ae\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f97925ebbd6fa09527b11375bc8e717\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f97925ebbd6fa09527b11375bc8e717\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0b876c1b7f5119d8d465be5cc7850ce\transformed\play-services-measurement-base-20.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0b876c1b7f5119d8d465be5cc7850ce\transformed\play-services-measurement-base-20.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0309cb612008fa0a7f120a51a87e98c5\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0309cb612008fa0a7f120a51a87e98c5\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2083bf72e6b404572a732073264ff144\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2083bf72e6b404572a732073264ff144\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b0d1513c313fd2f1285485128d1180e\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b0d1513c313fd2f1285485128d1180e\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\720f8bfa2f89870d854f88b902b470f8\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\720f8bfa2f89870d854f88b902b470f8\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b519326dc06e70070242a413ed557da\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b519326dc06e70070242a413ed557da\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3cb5d21b8c3286331b3e06cfcd4be8df\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3cb5d21b8c3286331b3e06cfcd4be8df\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f102f5f2af0f9920a4ec2bbb16286a0\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f102f5f2af0f9920a4ec2bbb16286a0\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.react:hermes-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\daebce6d293e79ddda89f722b2e71ba9\transformed\hermes-android-0.76.9-release\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\daebce6d293e79ddda89f722b2e71ba9\transformed\hermes-android-0.76.9-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32e32c61d7a8344f78f9e5ecbc1705ec\transformed\viewbinding-8.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32e32c61d7a8344f78f9e5ecbc1705ec\transformed\viewbinding-8.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d69efae93c03c835a3b5c84b11a1ec\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d69efae93c03c835a3b5c84b11a1ec\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97471c397bccf6da42a783affda4a2d9\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97471c397bccf6da42a783affda4a2d9\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1b3536639a988ae7b5cfd911455628f\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1b3536639a988ae7b5cfd911455628f\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb09bf49364a9fe47e8dca077ca1d888\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb09bf49364a9fe47e8dca077ca1d888\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ab96a97679138b4468581ca13933152\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ab96a97679138b4468581ca13933152\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c68e127b7ada1b8186bfd581f97604a3\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c68e127b7ada1b8186bfd581f97604a3\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\602e46738587ab61710877f6243c4b37\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\602e46738587ab61710877f6243c4b37\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8db616d994193aab5184ff3b00afbda\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8db616d994193aab5184ff3b00afbda\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52945983a2ba3c3e4198eb0a05864b2e\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52945983a2ba3c3e4198eb0a05864b2e\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\820c4f1e44229fb85aad13a17e92db82\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\820c4f1e44229fb85aad13a17e92db82\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10cf65477e02ebee6ddd7715742cb3aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10cf65477e02ebee6ddd7715742cb3aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63b29f9e1063f3908dbbea59d82125c9\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63b29f9e1063f3908dbbea59d82125c9\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de30b92ed9545aab089d3695f4f8c6bb\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de30b92ed9545aab089d3695f4f8c6bb\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58419a84b68c48343c7ffd5f50a82b6b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58419a84b68c48343c7ffd5f50a82b6b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01c68b7f99bce65bd3d6563c2b710b81\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01c68b7f99bce65bd3d6563c2b710b81\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d01a4295d9ec2fa124ca5810346eeaa\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d01a4295d9ec2fa124ca5810346eeaa\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f204f19a11d698f91d4acbb23137b35e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f204f19a11d698f91d4acbb23137b35e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\667cfa51b38ae60fa93671246cb6081b\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\667cfa51b38ae60fa93671246cb6081b\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc0bd38c684e6b7e213282ff4b2616bd\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc0bd38c684e6b7e213282ff4b2616bd\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe35776a6b655080e9af84e104a8e037\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe35776a6b655080e9af84e104a8e037\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e73ff42a7da05e4a493c8db2334e2b9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e73ff42a7da05e4a493c8db2334e2b9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\04f85e42967ab1fce55f7b61ebffddb3\transformed\vito-renderer-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\04f85e42967ab1fce55f7b61ebffddb3\transformed\vito-renderer-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07bf45f3c2473093a4271de984b194a6\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07bf45f3c2473093a4271de984b194a6\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.android.support:appcompat-v7:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf558a140f261b5d67b87ced93e193c2\transformed\appcompat-v7-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:appcompat-v7:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf558a140f261b5d67b87ced93e193c2\transformed\appcompat-v7-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fbjni:fbjni:0.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1957b0db02baa50e26af4d5b92e700f6\transformed\fbjni-0.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1957b0db02baa50e26af4d5b92e700f6\transformed\fbjni-0.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\94e6775a19cca0cd325a9f408af6a2dc\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\94e6775a19cca0cd325a9f408af6a2dc\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.android.support:support-fragment:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4486def0a1ddb0a16b14b527fdfb1f21\transformed\support-fragment-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-fragment:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4486def0a1ddb0a16b14b527fdfb1f21\transformed\support-fragment-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:animated-vector-drawable:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed7d39d8e0aceb01c08b7bdd080b1661\transformed\animated-vector-drawable-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:animated-vector-drawable:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed7d39d8e0aceb01c08b7bdd080b1661\transformed\animated-vector-drawable-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-core-ui:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fb9960c574b86629d1121e8e6eaf3cf\transformed\support-core-ui-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-core-ui:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fb9960c574b86629d1121e8e6eaf3cf\transformed\support-core-ui-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-core-utils:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83cec9bf728b2ea76114bd8ce62fb21e\transformed\support-core-utils-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-core-utils:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83cec9bf728b2ea76114bd8ce62fb21e\transformed\support-core-utils-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-vector-drawable:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bdf20d9fb3bc2986652116bbbce6b7b\transformed\support-vector-drawable-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-vector-drawable:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bdf20d9fb3bc2986652116bbbce6b7b\transformed\support-vector-drawable-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:loader:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a08d3be46356e97a4d38925042bb243\transformed\loader-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:loader:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a08d3be46356e97a4d38925042bb243\transformed\loader-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:viewpager:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\663ebbf54021bccc3bfe273e41cf299d\transformed\viewpager-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:viewpager:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\663ebbf54021bccc3bfe273e41cf299d\transformed\viewpager-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:coordinatorlayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bec8f7f3dc33ba79ba8bfc9c468757f3\transformed\coordinatorlayout-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:coordinatorlayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bec8f7f3dc33ba79ba8bfc9c468757f3\transformed\coordinatorlayout-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:drawerlayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\819b84d1fbf659aba3e29e4ab7917b57\transformed\drawerlayout-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:drawerlayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\819b84d1fbf659aba3e29e4ab7917b57\transformed\drawerlayout-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:slidingpanelayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d75aa8b6dd6f87e0b37baa5cb92e175\transformed\slidingpanelayout-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:slidingpanelayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d75aa8b6dd6f87e0b37baa5cb92e175\transformed\slidingpanelayout-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:customview:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4286670191318f1fd72103a5cb8bab3\transformed\customview-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:customview:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4286670191318f1fd72103a5cb8bab3\transformed\customview-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:swiperefreshlayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d140497454d262e7ae7fbe99b3f614a4\transformed\swiperefreshlayout-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:swiperefreshlayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d140497454d262e7ae7fbe99b3f614a4\transformed\swiperefreshlayout-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:asynclayoutinflater:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f1047bec2525d4e024f6dd76efef1de\transformed\asynclayoutinflater-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:asynclayoutinflater:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f1047bec2525d4e024f6dd76efef1de\transformed\asynclayoutinflater-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-compat:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b1f7ff49c8203eedd2425ac0bd53f5f\transformed\support-compat-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-compat:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b1f7ff49c8203eedd2425ac0bd53f5f\transformed\support-compat-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:versionedparcelable:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52188eee32d86784bab2f47543e011b7\transformed\versionedparcelable-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:versionedparcelable:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52188eee32d86784bab2f47543e011b7\transformed\versionedparcelable-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:cursoradapter:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42c837396384e45981cd41b8d128084f\transformed\cursoradapter-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:cursoradapter:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42c837396384e45981cd41b8d128084f\transformed\cursoradapter-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45850e6f0701a9f7c52949eb8f93999b\transformed\runtime-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45850e6f0701a9f7c52949eb8f93999b\transformed\runtime-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:documentfile:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3053c3395366edfec0592d439cc4594\transformed\documentfile-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:documentfile:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3053c3395366edfec0592d439cc4594\transformed\documentfile-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:localbroadcastmanager:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23b0318bf3288b448001701333dd7ba9\transformed\localbroadcastmanager-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:localbroadcastmanager:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23b0318bf3288b448001701333dd7ba9\transformed\localbroadcastmanager-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:print:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b5eb9e94ad725a09e96f0e81c7352d3\transformed\print-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:print:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b5eb9e94ad725a09e96f0e81c7352d3\transformed\print-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:viewmodel:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ba350c82b81d9c855bdcc34a6789626\transformed\viewmodel-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:viewmodel:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ba350c82b81d9c855bdcc34a6789626\transformed\viewmodel-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:livedata:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\257cf0f385254b513fb58820af5da348\transformed\livedata-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:livedata:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\257cf0f385254b513fb58820af5da348\transformed\livedata-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:livedata-core:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d19feff7181683dbab384d2a50904831\transformed\livedata-core-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:livedata-core:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d19feff7181683dbab384d2a50904831\transformed\livedata-core-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [android.arch.core:runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f259b13cb15c268d5bf7f4414c629cb0\transformed\runtime-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [android.arch.core:runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f259b13cb15c268d5bf7f4414c629cb0\transformed\runtime-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:interpolator:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7666d55386ce438417c89eb9eacc22d\transformed\interpolator-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:interpolator:28.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7666d55386ce438417c89eb9eacc22d\transformed\interpolator-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65e83cd3aee2760cef02a821b313c704\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65e83cd3aee2760cef02a821b313c704\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [:react-native-voice_voice] C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-voice\voice\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-51
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3a7ef0c6947795167b7587418640207\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3a7ef0c6947795167b7587418640207\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b56249db71bc4564ba5f782195824c95\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b56249db71bc4564ba5f782195824c95\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f97925ebbd6fa09527b11375bc8e717\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f97925ebbd6fa09527b11375bc8e717\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\602e46738587ab61710877f6243c4b37\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\602e46738587ab61710877f6243c4b37\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:22-76
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:9-45:48
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:107:9-109:62
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:107:9-109:62
	android:resource
		ADDED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-59
		REJECTED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:109:13-59
	tools:replace
		ADDED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:45:13-45
	android:name
		ADDED from [:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:13-65
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-64
	android:exported
		ADDED from [:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-83
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
	android:resource
		ADDED from [:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
	android:name
		ADDED from [:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
provider#expo.modules.clipboard.ClipboardFileProvider
ADDED from [:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-15:20
	android:authorities
		ADDED from [:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-73
	android:exported
		ADDED from [:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-36
	android:name
		ADDED from [:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-72
meta-data#expo.modules.clipboard.CLIPBOARD_FILE_PROVIDER_PATHS
ADDED from [:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-14:68
	android:resource
		ADDED from [:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-65
	android:name
		ADDED from [:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:17-84
intent#action:name:android.intent.action.OPEN_DOCUMENT+category:name:android.intent.category.DEFAULT+category:name:android.intent.category.OPENABLE+data:mimeType:*/*
ADDED from [:expo-document-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:9-17:18
action#android.intent.action.OPEN_DOCUMENT
ADDED from [:expo-document-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
	android:name
		ADDED from [:expo-document-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:21-71
category#android.intent.category.OPENABLE
ADDED from [:expo-document-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-73
	android:name
		ADDED from [:expo-document-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:23-70
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-79
	android:name
		ADDED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:13-48
	android:name
		ADDED from [:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-74
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-19:18
action#android.media.action.IMAGE_CAPTURE
ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-73
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:21-70
intent#action:name:android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:9-24:18
action#android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-80
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:21-77
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:9-40:19
	android:enabled
		ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-36
	android:exported
		ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:13-37
	tools:ignore
		ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:32:13-40
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:13-35:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:17-94
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:25-91
meta-data#photopicker_activity:0:required
ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:13-39:36
	android:value
		ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:17-33
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:17-63
activity#com.canhub.cropper.CropImageActivity
ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:9-44:59
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:33:9-35:39
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:33:9-35:39
	android:exported
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:35:13-36
	android:theme
		ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-56
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:13-64
provider#expo.modules.imagepicker.fileprovider.ImagePickerFileProvider
ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:46:9-54:20
	android:grantUriPermissions
		ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:50:13-47
	android:authorities
		ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:48:13-75
	android:exported
		ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:49:13-37
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:47:13-89
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-81
REJECTED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:5-81
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-74
service#expo.modules.notifications.service.ExpoFirebaseMessagingService
ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:9-17:19
	android:exported
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-37
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-91
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:29
	android:priority
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:28-49
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-78
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:25-75
receiver#expo.modules.notifications.service.NotificationsService
ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:20
	android:enabled
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-35
	android:exported
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-83
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:android.intent.action.REBOOT+action:name:com.htc.intent.action.QUICKBOOT_POWERON+action:name:expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-30:29
	android:priority
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:28-49
action#expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:17-88
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:25-85
action#android.intent.action.BOOT_COMPLETED
ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-79
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-76
action#android.intent.action.REBOOT
ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:17-71
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:25-68
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:25-79
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:25-79
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-84
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:25-81
activity#expo.modules.notifications.service.NotificationForwarderActivity
ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:9-40:75
	android:excludeFromRecents
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:35:13-46
	android:launchMode
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:13-42
	android:noHistory
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:13-37
	android:exported
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:13-37
	android:theme
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:40:13-72
	android:taskAffinity
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:13-36
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:13-92
intent#action:name:android.intent.action.SEND+data:mimeType:*/*
ADDED from [:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-14:18
action#android.intent.action.SEND
ADDED from [:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-65
	android:name
		ADDED from [:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:21-62
provider#expo.modules.sharing.SharingFileProvider
ADDED from [:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:9-26:20
	android:grantUriPermissions
		ADDED from [:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-47
	android:authorities
		ADDED from [:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-71
	android:exported
		ADDED from [:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-68
intent#action:name:android.intent.action.TTS_SERVICE
ADDED from [:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:18
action#android.intent.action.TTS_SERVICE
ADDED from [:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-72
	android:name
		ADDED from [:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:21-69
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\94e6775a19cca0cd325a9f408af6a2dc\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\94e6775a19cca0cd325a9f408af6a2dc\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\94e6775a19cca0cd325a9f408af6a2dc\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-57
meta-data#com.bumptech.glide.integration.okhttp3.OkHttpGlideModule
ADDED from [com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1dc3cf6a41b4f5c2729b5d5e6d5ceec\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:11:9-13:43
	android:value
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1dc3cf6a41b4f5c2729b5d5e6d5ceec\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:13:13-40
	android:name
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1dc3cf6a41b4f5c2729b5d5e6d5ceec\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:12:13-84
intent#action:name:android.intent.action.GET_CONTENT+category:name:android.intent.category.OPENABLE+data:mimeType:*/*
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:10:9-16:18
action#android.intent.action.GET_CONTENT
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:13-72
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:21-69
provider#com.canhub.cropper.CropFileProvider
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:23:9-31:20
	android:grantUriPermissions
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:27:13-47
	android:authorities
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:26:13-37
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:24:13-63
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:27:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:27:22-79
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:28:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:28:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:29:5-83
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:29:22-80
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:47:9-49:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:48:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:48:21-87
intent#action:name:android.intent.action.INSERT+data:mimeType:vnd.android.cursor.dir/event
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:52:9-56:18
action#android.intent.action.INSERT
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:53:13-67
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:53:21-64
intent#action:name:android.intent.action.VIEW+data:scheme:sms
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:57:9-61:18
intent#action:name:android.intent.action.DIAL+data:path:tel:
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:62:9-66:18
action#android.intent.action.DIAL
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:63:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:63:21-62
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:73:9-78:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:76:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:78:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:75:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:77:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:74:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:80:9-85:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:82:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:83:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:85:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:84:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:81:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:87:9-91:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:89:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:90:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:91:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:88:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:93:9-97:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:96:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:97:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:95:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:94:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:98:9-105:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:100:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:102:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:101:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:105:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:104:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:103:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:99:13-82
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3a7ef0c6947795167b7587418640207\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3a7ef0c6947795167b7587418640207\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91477e5d6c9216b70ac507e0b5e1f66e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91477e5d6c9216b70ac507e0b5e1f66e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10cf65477e02ebee6ddd7715742cb3aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10cf65477e02ebee6ddd7715742cb3aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91477e5d6c9216b70ac507e0b5e1f66e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91477e5d6c9216b70ac507e0b5e1f66e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91477e5d6c9216b70ac507e0b5e1f66e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc43d647cd71a26bfd564415b69e8be4\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc43d647cd71a26bfd564415b69e8be4\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc43d647cd71a26bfd564415b69e8be4\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc43d647cd71a26bfd564415b69e8be4\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867f28603a9adbd922e925087114cbc1\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867f28603a9adbd922e925087114cbc1\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad10a3280ed9ff2393dd52d1764f5de6\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad10a3280ed9ff2393dd52d1764f5de6\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b519326dc06e70070242a413ed557da\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b519326dc06e70070242a413ed557da\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:35:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867f28603a9adbd922e925087114cbc1\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867f28603a9adbd922e925087114cbc1\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867f28603a9adbd922e925087114cbc1\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa2d5bf76eb464c8ff4defe2fea1aac0\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa2d5bf76eb464c8ff4defe2fea1aac0\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa2d5bf76eb464c8ff4defe2fea1aac0\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.gokul719.snack97152fc1f368437dac54171df4ba22bd.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.gokul719.snack97152fc1f368437dac54171df4ba22bd.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad10a3280ed9ff2393dd52d1764f5de6\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad10a3280ed9ff2393dd52d1764f5de6\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad10a3280ed9ff2393dd52d1764f5de6\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0309cb612008fa0a7f120a51a87e98c5\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0309cb612008fa0a7f120a51a87e98c5\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0309cb612008fa0a7f120a51a87e98c5\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10cf65477e02ebee6ddd7715742cb3aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10cf65477e02ebee6ddd7715742cb3aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10cf65477e02ebee6ddd7715742cb3aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65e83cd3aee2760cef02a821b313c704\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65e83cd3aee2760cef02a821b313c704\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
