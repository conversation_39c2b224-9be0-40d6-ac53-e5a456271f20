{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeReleaseResources-79:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46429bd1dac210616d37d633e605f2e8\\transformed\\browser-1.8.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,371", "endColumns": "103,100,110,99", "endOffsets": "154,255,366,466"}, "to": {"startLines": "83,143,144,145", "startColumns": "4,4,4,4", "startOffsets": "7464,12155,12256,12367", "endColumns": "103,100,110,99", "endOffsets": "7563,12251,12362,12462"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,238", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "925,1030,1128,1240,1326,1432,1547,1625,1700,1792,1886,1982,2083,2190,2290,2394,2492,2590,2687,2769,2880,2982,3080,3187,3290,3394,3550,19669", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "1025,1123,1235,1321,1427,1542,1620,1695,1787,1881,1977,2078,2185,2285,2389,2487,2585,2682,2764,2875,2977,3075,3182,3285,3389,3545,3647,19746"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1c6965b61d3737d12e0f0d1e87d21cde\\transformed\\ui-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,289,387,487,574,659,751,840,928,1009,1093,1168,1243,1315,1385,1464,1530", "endColumns": "95,87,97,99,86,84,91,88,87,80,83,74,74,71,69,78,65,119", "endOffsets": "196,284,382,482,569,654,746,835,923,1004,1088,1163,1238,1310,1380,1459,1525,1645"}, "to": {"startLines": "62,63,87,88,89,155,156,209,210,228,229,239,243,246,248,253,254,256", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4846,4942,7812,7910,8010,13082,13167,17356,17445,19002,19083,19751,20075,20313,20456,20863,20942,21083", "endColumns": "95,87,97,99,86,84,91,88,87,80,83,74,74,71,69,78,65,119", "endOffsets": "4937,5025,7905,8005,8092,13162,13254,17440,17528,19078,19162,19821,20145,20380,20521,20937,21003,21198"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "52,53,54,55,56,57,58,251", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3811,3909,4011,4111,4211,4319,4424,20687", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "3904,4006,4106,4206,4314,4419,4537,20783"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\11b33a2f8f4c91a9de31219b5632fe82\\transformed\\Android-Image-Cropper-4.3.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,191,238,299,347,419,487,564,618,671,738", "endColumns": "135,46,60,47,71,67,76,53,52,66,54", "endOffsets": "186,233,294,342,414,482,559,613,666,733,788"}, "to": {"startLines": "84,85,86,147,148,149,150,151,223,224,225", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7568,7704,7751,12543,12591,12663,12731,12808,18657,18710,18777", "endColumns": "135,46,60,47,71,67,76,53,52,66,54", "endOffsets": "7699,7746,7807,12586,12658,12726,12803,12857,18705,18772,18827"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b56249db71bc4564ba5f782195824c95\\transformed\\exoplayer-core-2.18.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,196,270,342,419,486,583,674", "endColumns": "73,66,73,71,76,66,96,90,75", "endOffsets": "124,191,265,337,414,481,578,669,745"}, "to": {"startLines": "115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10225,10299,10366,10440,10512,10589,10656,10753,10844", "endColumns": "73,66,73,71,76,66,96,90,75", "endOffsets": "10294,10361,10435,10507,10584,10651,10748,10839,10915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,322,423,545,626,692,786,856,915,1023,1092,1150,1222,1286,1340,1468,1528,1590,1644,1722,1859,1951,2035,2150,2234,2320,2410,2477,2543,2617,2699,2792,2866,2944,3016,3090,3182,3264,3353,3442,3516,3594,3680,3735,3802,3882,3966,4028,4092,4155,4262,4366,4465,4571", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,83,100,121,80,65,93,69,58,107,68,57,71,63,53,127,59,61,53,77,136,91,83,114,83,85,89,66,65,73,81,92,73,77,71,73,91,81,88,88,73,77,85,54,66,79,83,61,63,62,106,103,98,105,81", "endOffsets": "233,317,418,540,621,687,781,851,910,1018,1087,1145,1217,1281,1335,1463,1523,1585,1639,1717,1854,1946,2030,2145,2229,2315,2405,2472,2538,2612,2694,2787,2861,2939,3011,3085,3177,3259,3348,3437,3511,3589,3675,3730,3797,3877,3961,4023,4087,4150,4257,4361,4460,4566,4648"}, "to": {"startLines": "19,51,59,60,61,90,142,152,157,158,159,160,161,162,163,164,165,166,167,168,169,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "742,3727,4542,4643,4765,8097,12061,12862,13259,13318,13426,13495,13553,13625,13689,13743,13871,13931,13993,14047,14125,14481,14573,14657,14772,14856,14942,15032,15099,15165,15239,15321,15414,15488,15566,15638,15712,15804,15886,15975,16064,16138,16216,16302,16357,16424,16504,16588,16650,16714,16777,16884,16988,17087,18575", "endLines": "22,51,59,60,61,90,142,152,157,158,159,160,161,162,163,164,165,166,167,168,169,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,222", "endColumns": "12,83,100,121,80,65,93,69,58,107,68,57,71,63,53,127,59,61,53,77,136,91,83,114,83,85,89,66,65,73,81,92,73,77,71,73,91,81,88,88,73,77,85,54,66,79,83,61,63,62,106,103,98,105,81", "endOffsets": "920,3806,4638,4760,4841,8158,12150,12927,13313,13421,13490,13548,13620,13684,13738,13866,13926,13988,14042,14120,14257,14568,14652,14767,14851,14937,15027,15094,15160,15234,15316,15409,15483,15561,15633,15707,15799,15881,15970,16059,16133,16211,16297,16352,16419,16499,16583,16645,16709,16772,16879,16983,17082,17188,18652"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495d9b066df23cebe365f0b1f0fc7029\\transformed\\exoplayer-ui-2.18.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,311,501,692,779,867,942,1032,1118,1197,1262,1366,1470,1539,1609,1681,1750,1877,2005,2138,2211,2295,2371,2448,2535,2623,2689,2754,2807,2867,2915,2976,3048,3118,3183,3254,3319,3377,3443,3507,3573,3625,3687,3763,3839", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,86,87,74,89,85,78,64,103,103,68,69,71,68,126,127,132,72,83,75,76,86,87,65,64,52,59,47,60,71,69,64,70,64,57,65,63,65,51,61,75,75,55", "endOffsets": "306,496,687,774,862,937,1027,1113,1192,1257,1361,1465,1534,1604,1676,1745,1872,2000,2133,2206,2290,2366,2443,2530,2618,2684,2749,2802,2862,2910,2971,3043,3113,3178,3249,3314,3372,3438,3502,3568,3620,3682,3758,3834,3890"}, "to": {"startLines": "2,11,15,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,361,551,8163,8250,8338,8413,8503,8589,8668,8733,8837,8941,9010,9080,9152,9221,9348,9476,9609,9682,9766,9842,9919,10006,10094,10160,10920,10973,11033,11081,11142,11214,11284,11349,11420,11485,11543,11609,11673,11739,11791,11853,11929,12005", "endLines": "10,14,18,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "endColumns": "17,12,12,86,87,74,89,85,78,64,103,103,68,69,71,68,126,127,132,72,83,75,76,86,87,65,64,52,59,47,60,71,69,64,70,64,57,65,63,65,51,61,75,75,55", "endOffsets": "356,546,737,8245,8333,8408,8498,8584,8663,8728,8832,8936,9005,9075,9147,9216,9343,9471,9604,9677,9761,9837,9914,10001,10089,10155,10220,10968,11028,11076,11137,11209,11279,11344,11415,11480,11538,11604,11668,11734,11786,11848,11924,12000,12056"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c7a5afab22fc7e54eb8c70c357abcc1\\transformed\\react-android-0.76.9-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,213,289,357,439,506,582,658,741,828,909,992,1072,1158,1243,1321,1392,1462,1553,1628,1703", "endColumns": "74,82,75,67,81,66,75,75,82,86,80,82,79,85,84,77,70,69,90,74,74,77", "endOffsets": "125,208,284,352,434,501,577,653,736,823,904,987,1067,1153,1238,1316,1387,1457,1548,1623,1698,1776"}, "to": {"startLines": "50,64,146,153,154,170,171,172,226,227,237,240,241,242,244,245,247,249,250,252,255,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3652,5030,12467,12932,13000,14262,14329,14405,18832,18915,19588,19826,19909,19989,20150,20235,20385,20526,20596,20788,21008,21203", "endColumns": "74,82,75,67,81,66,75,75,82,86,80,82,79,85,84,77,70,69,90,74,74,77", "endOffsets": "3722,5108,12538,12995,13077,14324,14400,14476,18910,18997,19664,19904,19984,20070,20230,20308,20451,20591,20682,20858,21078,21276"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\659eba141f87d2afd24e0f28711a233e\\transformed\\play-services-ads-23.6.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,294,350,416,488,576,640,782,901,1019,1069,1127,1259,1348,1390,1488,1525,1561,1613,1702,1741", "endColumns": "40,53,55,65,71,87,63,141,118,117,49,57,131,88,41,97,36,35,51,88,38,55", "endOffsets": "239,293,349,415,487,575,639,781,900,1018,1068,1126,1258,1347,1389,1487,1524,1560,1612,1701,1740,1796"}, "to": {"startLines": "206,207,208,211,212,213,214,215,216,217,218,219,220,221,230,231,232,233,234,235,236,260", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17193,17238,17296,17533,17603,17679,17771,17839,17985,18108,18230,18284,18346,18482,19167,19213,19315,19356,19396,19452,19545,21458", "endColumns": "44,57,59,69,75,91,67,145,122,121,53,61,135,92,45,101,40,39,55,92,42,59", "endOffsets": "17233,17291,17351,17598,17674,17766,17834,17980,18103,18225,18279,18341,18477,18570,19208,19310,19351,19391,19447,19540,19583,21513"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dc43d647cd71a26bfd564415b69e8be4\\transformed\\play-services-base-18.0.1\\res\\values-de\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,458,582,690,864,991,1108,1223,1399,1507,1672,1799,1957,2129,2196,2255", "endColumns": "104,159,123,107,173,126,116,114,175,107,164,126,157,171,66,58,75", "endOffsets": "297,457,581,689,863,990,1107,1222,1398,1506,1671,1798,1956,2128,2195,2254,2330"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5113,5222,5386,5514,5626,5804,5935,6056,6320,6500,6612,6781,6912,7074,7250,7321,7384", "endColumns": "108,163,127,111,177,130,120,118,179,111,168,130,161,175,70,62,79", "endOffsets": "5217,5381,5509,5621,5799,5930,6051,6170,6495,6607,6776,6907,7069,7245,7316,7379,7459"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bbb068f5d1da3b4f3e1ef6a35abc59e\\transformed\\foundation-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,89", "endOffsets": "137,227"}, "to": {"startLines": "258,259", "startColumns": "4,4", "startOffsets": "21281,21368", "endColumns": "86,89", "endOffsets": "21363,21453"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0309cb612008fa0a7f120a51a87e98c5\\transformed\\play-services-basement-18.4.0\\res\\values-de\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6175", "endColumns": "144", "endOffsets": "6315"}}]}]}