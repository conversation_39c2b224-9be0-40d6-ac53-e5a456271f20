import { Platform } from 'react-native';

// Try to import native ads, fall back gracefully if not available
let mobileAds, BannerAd, BannerAdSize, InterstitialAd, AdEventType, TestIds;
try {
  const GoogleMobileAds = require('react-native-google-mobile-ads');
  mobileAds = GoogleMobileAds.default;
  BannerAd = GoogleMobileAds.BannerAd;
  BannerAdSize = GoogleMobileAds.BannerAdSize;
  InterstitialAd = GoogleMobileAds.InterstitialAd;
  AdEventType = GoogleMobileAds.AdEventType;
  TestIds = GoogleMobileAds.TestIds;
} catch (_error) {
  console.log('Native Google Mobile Ads not available, using fallback');
  mobileAds = null;
  BannerAd = null;
  BannerAdSize = null;
  InterstitialAd = null;
  AdEventType = null;
  TestIds = {
    BANNER: 'ca-app-pub-3940256099942544/6300978111',
    INTERSTITIAL: 'ca-app-pub-3940256099942544/1033173712'
  };
}

// Initialize the Google Mobile Ads SDK with enhanced error handling and retry mechanism
export const initializeAdMob = (retryCount = 0) => {
  return new Promise((resolve, reject) => {
    // If native ads are not available, resolve immediately
    if (!mobileAds) {
      console.log('Native Google Mobile Ads not available, skipping initialization');
      resolve({ fallback: true });
      return;
    }

    const maxRetries = 3;
    const baseTimeout = 15000; // Increased to 15 seconds
    const currentTimeout = baseTimeout + (retryCount * 5000); // Progressive timeout

    try {
      console.log(`Initializing Google Mobile Ads SDK... (Attempt ${retryCount + 1}/${maxRetries + 1})`);

      // Progressive timeout based on retry count
      const timeoutId = setTimeout(() => {
        console.warn(`AdMob initialization timed out after ${currentTimeout}ms (Attempt ${retryCount + 1})`);

        if (retryCount < maxRetries) {
          console.log(`Retrying AdMob initialization in 2 seconds...`);
          setTimeout(() => {
            initializeAdMob(retryCount + 1)
              .then(resolve)
              .catch(reject);
          }, 2000);
        } else {
          console.error('AdMob initialization failed after all retries');
          // Don't reject, resolve with fallback to keep app working
          resolve({ fallback: true, error: 'Initialization timeout after retries' });
        }
      }, currentTimeout);

      mobileAds()
        .initialize()
        .then(adapterStatuses => {
          clearTimeout(timeoutId);

          // Check adapter statuses to verify initialization
          const readyAdapters = Object.entries(adapterStatuses).filter(([_, status]) => status.state === 1);
          const failedAdapters = Object.entries(adapterStatuses).filter(([_, status]) => status.state !== 1);

          console.log(`AdMob initialization completed: ${readyAdapters.length} ready, ${failedAdapters.length} failed`);

          if (readyAdapters.length > 0) {
            console.log('Google Mobile Ads SDK initialized successfully!');
            if (failedAdapters.length > 0) {
              console.warn('Some adapters failed to initialize:',
                failedAdapters.map(([name, status]) => `${name}: ${status.description}`).join(', ')
              );
            }
            resolve({
              success: true,
              adapterStatuses,
              readyCount: readyAdapters.length,
              failedCount: failedAdapters.length
            });
          } else if (retryCount < maxRetries) {
            console.warn('No adapters ready, retrying...');
            setTimeout(() => {
              initializeAdMob(retryCount + 1)
                .then(resolve)
                .catch(reject);
            }, 2000);
          } else {
            console.error('No adapters ready after all retries');
            resolve({ fallback: true, error: 'No adapters ready' });
          }
        })
        .catch(error => {
          clearTimeout(timeoutId);
          console.error(`AdMob initialization failed (Attempt ${retryCount + 1}):`, error);

          if (retryCount < maxRetries) {
            console.log(`Retrying AdMob initialization in 3 seconds...`);
            setTimeout(() => {
              initializeAdMob(retryCount + 1)
                .then(resolve)
                .catch(reject);
            }, 3000);
          } else {
            console.error('AdMob initialization failed after all retries');
            // Don't reject, resolve with fallback to keep app working
            resolve({ fallback: true, error: error.message });
          }
        });
    } catch (error) {
      console.error('Error during AdMob setup:', error);
      if (retryCount < maxRetries) {
        setTimeout(() => {
          initializeAdMob(retryCount + 1)
            .then(resolve)
            .catch(reject);
        }, 3000);
      } else {
        resolve({ fallback: true, error: error.message });
      }
    }
  });
};

// Ad unit IDs
// For production, replace these with your actual ad unit IDs
export const adUnitIds = {
  // Banner ad unit IDs
  banner: __DEV__
    ? TestIds.BANNER
    : Platform.select({
        ios: 'ca-app-pub-9706687137550019/4124160377', // Using same banner ad unit ID for iOS
        android: 'ca-app-pub-9706687137550019/4124160377', // Updated with your real Android banner ad unit ID
      }),

  // Interstitial ad unit IDs
  interstitial: __DEV__
    ? TestIds.INTERSTITIAL
    : Platform.select({
        ios: 'ca-app-pub-9706687137550019/7998992050', // Using your real interstitial ad unit ID for iOS
        android: 'ca-app-pub-9706687137550019/7998992050', // Using your real interstitial ad unit ID for Android
      }),
};

// Load and show interstitial ad
export const loadInterstitialAd = (onAdLoaded = () => {}, onAdDismissed = () => {}) => {
  // If native ads are not available, skip interstitial
  if (!InterstitialAd || !AdEventType) {
    console.log('Interstitial ads not available, skipping');
    onAdDismissed();
    return null;
  }

  try {
    const interstitial = InterstitialAd.createForAdRequest(adUnitIds.interstitial);

    // Add event listeners
    const unsubscribeLoaded = interstitial.addAdEventListener(AdEventType.LOADED, () => {
      console.log('Interstitial ad loaded');
      onAdLoaded();
    });

    const unsubscribeClosed = interstitial.addAdEventListener(AdEventType.CLOSED, () => {
      console.log('Interstitial ad closed');
      onAdDismissed();
      // Clean up listeners
      unsubscribeLoaded();
      unsubscribeClosed();
      unsubscribeError();
    });

    const unsubscribeError = interstitial.addAdEventListener(AdEventType.ERROR, (error) => {
      console.error('Interstitial ad error:', error);
      // Still call the dismiss handler so app flow continues
      onAdDismissed();
      // Clean up listeners
      unsubscribeLoaded();
      unsubscribeClosed();
      unsubscribeError();
    });

    // Load the interstitial ad
    interstitial.load();

    return interstitial;
  } catch (error) {
    console.error('Failed to create interstitial ad:', error);
    // Call dismiss handler immediately so app flow continues
    onAdDismissed();
    return null;
  }
};

// Export BannerAd and BannerAdSize for direct use in components
export { BannerAd, BannerAdSize };