{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeReleaseResources-79:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "51,52,53,54,55,56,57,228", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3622,3718,3821,3920,4018,4119,4217,18197", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "3713,3816,3915,4013,4114,4212,4323,18293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dc43d647cd71a26bfd564415b69e8be4\\transformed\\play-services-base-18.0.1\\res\\values-lo\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,463,589,692,840,961,1065,1177,1325,1426,1588,1713,1880,2037,2101,2166", "endColumns": "103,165,125,102,147,120,103,111,147,100,161,124,166,156,63,64,80", "endOffsets": "296,462,588,691,839,960,1064,1176,1324,1425,1587,1712,1879,2036,2100,2165,2246"}, "to": {"startLines": "64,65,66,67,68,69,70,71,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4888,4996,5166,5296,5403,5555,5680,5788,6036,6188,6293,6459,6588,6759,6920,6988,7057", "endColumns": "107,169,129,106,151,124,107,115,151,104,165,128,170,160,67,68,84", "endOffsets": "4991,5161,5291,5398,5550,5675,5783,5899,6183,6288,6454,6583,6754,6915,6983,7052,7137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0309cb612008fa0a7f120a51a87e98c5\\transformed\\play-services-basement-18.4.0\\res\\values-lo\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "72", "startColumns": "4", "startOffsets": "5904", "endColumns": "131", "endOffsets": "6031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,221", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "874,977,1080,1193,1278,1382,1493,1571,1648,1739,1832,1924,2018,2118,2211,2306,2402,2493,2584,2665,2772,2876,2974,3077,3181,3285,3442,17668", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "972,1075,1188,1273,1377,1488,1566,1643,1734,1827,1919,2013,2113,2206,2301,2397,2488,2579,2660,2767,2871,2969,3072,3176,3280,3437,3536,17745"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bbb068f5d1da3b4f3e1ef6a35abc59e\\transformed\\foundation-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,84", "endOffsets": "136,221"}, "to": {"startLines": "232,233", "startColumns": "4,4", "startOffsets": "18563,18649", "endColumns": "85,84", "endOffsets": "18644,18729"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1c6965b61d3737d12e0f0d1e87d21cde\\transformed\\ui-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,272,381,479,568,657,747,833,916,996,1080,1154,1235,1310,1385,1463,1529", "endColumns": "89,76,108,97,88,88,89,85,82,79,83,73,80,74,74,77,65,120", "endOffsets": "190,267,376,474,563,652,742,828,911,991,1075,1149,1230,1305,1380,1458,1524,1645"}, "to": {"startLines": "61,62,83,84,85,145,146,198,199,212,213,222,223,224,226,229,230,231", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4645,4735,7240,7349,7447,11905,11994,15920,16006,17137,17217,17750,17824,17905,18051,18298,18376,18442", "endColumns": "89,76,108,97,88,88,89,85,82,79,83,73,80,74,74,77,65,120", "endOffsets": "4730,4807,7344,7442,7531,11989,12079,16001,16084,17212,17296,17819,17900,17975,18121,18371,18437,18558"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495d9b066df23cebe365f0b1f0fc7029\\transformed\\exoplayer-ui-2.18.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,477,655,737,817,894,982,1064,1140,1204,1297,1389,1459,1523,1586,1656,1766,1873,1983,2051,2128,2198,2274,2358,2440,2502,2565,2618,2676,2724,2785,2844,2912,2973,3039,3103,3162,3226,3293,3360,3414,3474,3548,3622", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,79,76,87,81,75,63,92,91,69,63,62,69,109,106,109,67,76,69,75,83,81,61,62,52,57,47,60,58,67,60,65,63,58,63,66,66,53,59,73,73,55", "endOffsets": "281,472,650,732,812,889,977,1059,1135,1199,1292,1384,1454,1518,1581,1651,1761,1868,1978,2046,2123,2193,2269,2353,2435,2497,2560,2613,2671,2719,2780,2839,2907,2968,3034,3098,3157,3221,3288,3355,3409,3469,3543,3617,3673"}, "to": {"startLines": "2,11,15,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,527,7602,7684,7764,7841,7929,8011,8087,8151,8244,8336,8406,8470,8533,8603,8713,8820,8930,8998,9075,9145,9221,9305,9387,9449,10169,10222,10280,10328,10389,10448,10516,10577,10643,10707,10766,10830,10897,10964,11018,11078,11152,11226", "endLines": "10,14,18,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137", "endColumns": "17,12,12,81,79,76,87,81,75,63,92,91,69,63,62,69,109,106,109,67,76,69,75,83,81,61,62,52,57,47,60,58,67,60,65,63,58,63,66,66,53,59,73,73,55", "endOffsets": "331,522,700,7679,7759,7836,7924,8006,8082,8146,8239,8331,8401,8465,8528,8598,8708,8815,8925,8993,9070,9140,9216,9300,9382,9444,9507,10217,10275,10323,10384,10443,10511,10572,10638,10702,10761,10825,10892,10959,11013,11073,11147,11221,11277"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c7a5afab22fc7e54eb8c70c357abcc1\\transformed\\react-android-0.76.9-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,131,201,283,350,417,488", "endColumns": "75,69,81,66,66,70,70", "endOffsets": "126,196,278,345,412,483,554"}, "to": {"startLines": "63,143,144,148,161,225,227", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4812,11753,11823,12143,13089,17980,18126", "endColumns": "75,69,81,66,66,70,70", "endOffsets": "4883,11818,11900,12205,13151,18046,18192"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46429bd1dac210616d37d633e605f2e8\\transformed\\browser-1.8.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,251,366", "endColumns": "97,97,114,99", "endOffsets": "148,246,361,461"}, "to": {"startLines": "82,139,140,141", "startColumns": "4,4,4,4", "startOffsets": "7142,11371,11469,11584", "endColumns": "97,97,114,99", "endOffsets": "7235,11464,11579,11679"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\659eba141f87d2afd24e0f28711a233e\\transformed\\play-services-ads-23.6.0\\res\\values-lo\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,247,294,348,413,481,577,640,762,858,972,1026,1084,1196,1273,1313,1391,1425,1460,1505,1572,1612", "endColumns": "47,46,53,64,67,95,62,121,95,113,53,57,111,76,39,77,33,34,44,66,39,55", "endOffsets": "246,293,347,412,480,576,639,761,857,971,1025,1083,1195,1272,1312,1390,1424,1459,1504,1571,1611,1667"}, "to": {"startLines": "195,196,197,200,201,202,203,204,205,206,207,208,209,210,214,215,216,217,218,219,220,234", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15759,15811,15862,16089,16158,16230,16330,16397,16523,16623,16741,16799,16861,16977,17301,17345,17427,17465,17504,17553,17624,18734", "endColumns": "51,50,57,68,71,99,66,125,99,117,57,61,115,80,43,81,37,38,48,70,43,59", "endOffsets": "15806,15857,15915,16153,16225,16325,16392,16518,16618,16736,16794,16856,16972,17053,17340,17422,17460,17499,17548,17619,17663,18789"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b56249db71bc4564ba5f782195824c95\\transformed\\exoplayer-core-2.18.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,185,251,316,391,461,553,640", "endColumns": "68,60,65,64,74,69,91,86,71", "endOffsets": "119,180,246,311,386,456,548,635,707"}, "to": {"startLines": "111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9512,9581,9642,9708,9773,9848,9918,10010,10097", "endColumns": "68,60,65,64,74,69,91,86,71", "endOffsets": "9576,9637,9703,9768,9843,9913,10005,10092,10164"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,305,420,539,622,688,777,846,905,1000,1065,1123,1188,1249,1309,1415,1476,1536,1594,1665,1784,1870,1952,2065,2140,2216,2306,2373,2439,2508,2582,2661,2734,2811,2880,2950,3035,3110,3203,3296,3370,3439,3533,3585,3652,3736,3820,3882,3946,4009,4108,4200,4295,4387", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,80,114,118,82,65,88,68,58,94,64,57,64,60,59,105,60,59,57,70,118,85,81,112,74,75,89,66,65,68,73,78,72,76,68,69,84,74,92,92,73,68,93,51,66,83,83,61,63,62,98,91,94,91,78", "endOffsets": "219,300,415,534,617,683,772,841,900,995,1060,1118,1183,1244,1304,1410,1471,1531,1589,1660,1779,1865,1947,2060,2135,2211,2301,2368,2434,2503,2577,2656,2729,2806,2875,2945,3030,3105,3198,3291,3365,3434,3528,3580,3647,3731,3815,3877,3941,4004,4103,4195,4290,4382,4461"}, "to": {"startLines": "19,50,58,59,60,86,138,142,147,149,150,151,152,153,154,155,156,157,158,159,160,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "705,3541,4328,4443,4562,7536,11282,11684,12084,12210,12305,12370,12428,12493,12554,12614,12720,12781,12841,12899,12970,13156,13242,13324,13437,13512,13588,13678,13745,13811,13880,13954,14033,14106,14183,14252,14322,14407,14482,14575,14668,14742,14811,14905,14957,15024,15108,15192,15254,15318,15381,15480,15572,15667,17058", "endLines": "22,50,58,59,60,86,138,142,147,149,150,151,152,153,154,155,156,157,158,159,160,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,211", "endColumns": "12,80,114,118,82,65,88,68,58,94,64,57,64,60,59,105,60,59,57,70,118,85,81,112,74,75,89,66,65,68,73,78,72,76,68,69,84,74,92,92,73,68,93,51,66,83,83,61,63,62,98,91,94,91,78", "endOffsets": "869,3617,4438,4557,4640,7597,11366,11748,12138,12300,12365,12423,12488,12549,12609,12715,12776,12836,12894,12965,13084,13237,13319,13432,13507,13583,13673,13740,13806,13875,13949,14028,14101,14178,14247,14317,14402,14477,14570,14663,14737,14806,14900,14952,15019,15103,15187,15249,15313,15376,15475,15567,15662,15754,17132"}}]}]}