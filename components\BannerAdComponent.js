import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import AdManager from '../src/services/AdManager';
import { AD_PLACEMENTS, AD_SIZES, REVENUE_CONFIG } from '../src/config/adConfig';
import SimpleAdBanner from './SimpleAdBanner';

// Try to import native ads, fall back gracefully if not available
let BannerAd, BannerAdSize;
try {
  const GoogleMobileAds = require('react-native-google-mobile-ads');
  BannerAd = GoogleMobileAds.BannerAd;
  BannerAdSize = GoogleMobileAds.BannerAdSize;
} catch (_error) {
  console.log('Native Google Mobile Ads not available, using WebView fallback');
  BannerAd = null;
  BannerAdSize = null;
}

const BannerAdComponent = ({
  size = BannerAdSize?.BANNER || AD_SIZES.RESPONSIVE_BANNER,
  placement = AD_PLACEMENTS.HOME_BANNER,
  fallbackToWebView = true,
  style = {},
  enableRefresh = true
}) => {
  const [adError, setAdError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [showFallback, setShowFallback] = useState(!BannerAd); // Show fallback if native ads not available
  const [refreshKey, setRefreshKey] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [loadStartTime, setLoadStartTime] = useState(null);
  const refreshIntervalRef = useRef(null);
  const loadTimeoutRef = useRef(null);

  const maxRetries = 8; // Increased for better fill rate on slow devices
  const baseLoadTimeout = 10000; // 10 seconds base timeout
  const progressiveTimeout = baseLoadTimeout + (retryCount * 3000); // Progressive timeout
  const unitId = AdManager.getAdUnitForPlacement(placement);
  const requestConfig = AdManager.getAdRequestConfig(placement);

  // Setup banner refresh interval for revenue optimization
  useEffect(() => {
    if (enableRefresh && REVENUE_CONFIG.enableBannerRefresh && !showFallback) {
      const interval = REVENUE_CONFIG.bannerRefreshInterval;

      refreshIntervalRef.current = setInterval(() => {
        if (AdManager.shouldRefreshBanner(placement)) {
          console.log(`Refreshing banner ad for ${placement}`);
          setRefreshKey(prev => prev + 1);
          AdManager.recordBannerRefresh(placement);
        }
      }, interval);

      return () => {
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current);
        }
      };
    }
  }, [placement, enableRefresh, showFallback]);

  useEffect(() => {
    // If native ads are not available, always show fallback
    if (!BannerAd) {
      setShowFallback(true);
      return;
    }

    // Check if this placement has excessive errors
    if (AdManager.hasExcessiveErrors(placement, 'banner')) {
      console.log(`Banner ad blocked for ${placement} due to excessive errors`);
      setShowFallback(true);
    }
  }, [placement]);

  const handleAdLoaded = () => {
    const loadTime = loadStartTime ? Date.now() - loadStartTime : 0;
    console.log(`Banner ad loaded successfully for ${placement} in ${loadTime}ms`);

    // Clear any pending timeout
    if (loadTimeoutRef.current) {
      clearTimeout(loadTimeoutRef.current);
      loadTimeoutRef.current = null;
    }

    setAdError(false);
    setRetryCount(0);
    setIsLoading(false);
    setLoadStartTime(null);
    AdManager.recordImpression(placement, 'banner');
    AdManager.resetErrors(placement, 'banner');
  };

  const handleAdFailedToLoad = (error) => {
    const loadTime = loadStartTime ? Date.now() - loadStartTime : 0;
    console.error(`Banner ad failed to load for ${placement} after ${loadTime}ms:`, error);

    // Clear any pending timeout
    if (loadTimeoutRef.current) {
      clearTimeout(loadTimeoutRef.current);
      loadTimeoutRef.current = null;
    }

    AdManager.recordError(placement, 'banner', error);

    if (retryCount < maxRetries) {
      setRetryCount(prev => prev + 1);
      console.log(`Retrying banner ad load (${retryCount + 1}/${maxRetries}) with ${progressiveTimeout}ms timeout`);

      // Set up progressive timeout for next attempt
      setLoadStartTime(Date.now());
      loadTimeoutRef.current = setTimeout(() => {
        console.warn(`Banner ad load timeout for ${placement} after ${progressiveTimeout}ms`);
        if (retryCount >= maxRetries - 1) {
          setAdError(true);
          setIsLoading(false);
          if (fallbackToWebView) {
            setShowFallback(true);
          }
        }
      }, progressiveTimeout);

      // The BannerAd component will automatically retry
    } else {
      setAdError(true);
      setIsLoading(false);
      if (fallbackToWebView) {
        setShowFallback(true);
      }
    }
  };

  // If we should show fallback or there's an error with fallback enabled
  if (showFallback || (adError && fallbackToWebView)) {
    return (
      <View style={[styles.container, style]}>
        <SimpleAdBanner height={60} testMode={false} />
      </View>
    );
  }

  // If there's an error and no fallback, show empty container
  if (adError) {
    return <View style={[styles.container, styles.emptyContainer, style]} />;
  }

  // If native ads are available, render them
  if (BannerAd) {
    return (
      <View style={[styles.container, style]}>
        <BannerAd
          key={`${placement}-${refreshKey}`} // Force refresh when key changes
          unitId={unitId}
          size={size}
          requestOptions={requestConfig}
          onAdLoaded={handleAdLoaded}
          onAdFailedToLoad={handleAdFailedToLoad}
          onAdClicked={() => AdManager.recordClick(placement, 'banner')}
        />
      </View>
    );
  }

  // Fallback if native ads are not available
  return (
    <View style={[styles.container, style]}>
      <SimpleAdBanner height={60} testMode={false} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    // Add bottom margin for iOS to account for the safe area
    marginBottom: Platform.OS === 'ios' ? 20 : 0,
  },
  emptyContainer: {
    height: 50, // Approximate height of a banner ad
  },
});

export default BannerAdComponent;