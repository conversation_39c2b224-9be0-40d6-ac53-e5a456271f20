import React from 'react';
import { View, StyleSheet, Platform, Dimensions, Text, TouchableOpacity, Linking } from 'react-native';

// Get screen width for responsive ad sizing
const windowWidth = Dimensions.get('window').width;

const SimpleAdBanner = ({ height = 50, testMode = false }) => {
  // Handle ad click - opens a sample advertiser page
  const handleAdClick = () => {
    if (!testMode) {
      // In a real implementation, this would track the click and redirect to the advertiser
      Linking.openURL('https://www.google.com/search?q=education+apps');
    }
  };

  if (testMode) {
    return (
      <View style={[styles.container, { height }]}>
        <View style={styles.testAdContainer}>
          <Text style={styles.testAdText}>
            TEST MODE - Ad Banner Placeholder{'\n'}
            ({windowWidth}x{height})
          </Text>
        </View>
      </View>
    );
  }

  // Simple native ad banner without WebView
  return (
    <TouchableOpacity
      style={[styles.container, { height }]}
      onPress={handleAdClick}
      activeOpacity={0.8}
    >
      <View style={styles.adBanner}>
        <View style={styles.adContent}>
          <Text style={styles.adTitle}>📚 Boost Your Learning</Text>
          <Text style={styles.adDescription}>Discover premium study tools & resources</Text>
        </View>
        <View style={styles.adLabel}>
          <Text style={styles.adLabelText}>Ad</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    overflow: 'hidden',
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    marginVertical: 4,
  },
  testAdContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#e0e0e0',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
  },
  testAdText: {
    color: '#555',
    fontSize: 12,
    textAlign: 'center',
    fontFamily: Platform.OS === 'ios' ? 'Arial' : 'sans-serif',
  },
  adBanner: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 8,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  adContent: {
    flex: 1,
    justifyContent: 'center',
  },
  adTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  adDescription: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
  },
  adLabel: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },
  adLabelText: {
    fontSize: 10,
    color: '#888',
    fontWeight: '500',
  },
});

export default SimpleAdBanner; 