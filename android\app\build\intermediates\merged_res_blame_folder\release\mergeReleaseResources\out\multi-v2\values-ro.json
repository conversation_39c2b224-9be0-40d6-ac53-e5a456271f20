{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeReleaseResources-79:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,334,447,531,636,755,840,920,1011,1104,1199,1293,1393,1486,1581,1675,1766,1858,1939,2049,2157,2255,2367,2473,2577,2739,2840", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "223,329,442,526,631,750,835,915,1006,1099,1194,1288,1388,1481,1576,1670,1761,1853,1934,2044,2152,2250,2362,2468,2572,2734,2835,2917"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,231", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1123,1246,1352,1465,1549,1654,1773,1858,1938,2029,2122,2217,2311,2411,2504,2599,2693,2784,2876,2957,3067,3175,3273,3385,3491,3595,3757,19031", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "1241,1347,1460,1544,1649,1768,1853,1933,2024,2117,2212,2306,2406,2499,2594,2688,2779,2871,2952,3062,3170,3268,3380,3486,3590,3752,3853,19108"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c7a5afab22fc7e54eb8c70c357abcc1\\transformed\\react-android-0.76.9-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,204,274,345,429,497,573,657,742,832,901,985,1075,1150,1232,1311,1389,1467,1541,1626,1699,1775", "endColumns": "69,78,69,70,83,67,75,83,84,89,68,83,89,74,81,78,77,77,73,84,72,75,84", "endOffsets": "120,199,269,340,424,492,568,652,737,827,896,980,1070,1145,1227,1306,1384,1462,1536,1621,1694,1770,1855"}, "to": {"startLines": "53,67,146,148,149,165,166,167,218,219,222,230,233,234,235,237,238,240,242,243,245,248,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3858,5229,12298,12442,12513,13734,13802,13878,18106,18191,18459,18947,19186,19276,19351,19507,19586,19743,19896,19970,20156,20373,20564", "endColumns": "69,78,69,70,83,67,75,83,84,89,68,83,89,74,81,78,77,77,73,84,72,75,84", "endOffsets": "3923,5303,12363,12508,12592,13797,13873,13957,18186,18276,18523,19026,19271,19346,19428,19581,19659,19816,19965,20050,20224,20444,20644"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0309cb612008fa0a7f120a51a87e98c5\\transformed\\play-services-basement-18.4.0\\res\\values-ro\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "139", "endOffsets": "334"}, "to": {"startLines": "76", "startColumns": "4", "startOffsets": "6328", "endColumns": "143", "endOffsets": "6467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b56249db71bc4564ba5f782195824c95\\transformed\\exoplayer-core-2.18.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,200,265,337,415,495,585,678", "endColumns": "80,63,64,71,77,79,89,92,73", "endOffsets": "131,195,260,332,410,490,580,673,747"}, "to": {"startLines": "115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10062,10143,10207,10272,10344,10422,10502,10592,10685", "endColumns": "80,63,64,71,77,79,89,92,73", "endOffsets": "10138,10202,10267,10339,10417,10497,10587,10680,10754"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bbb068f5d1da3b4f3e1ef6a35abc59e\\transformed\\foundation-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,150", "endColumns": "94,99", "endOffsets": "145,245"}, "to": {"startLines": "251,252", "startColumns": "4,4", "startOffsets": "20649,20744", "endColumns": "94,99", "endOffsets": "20739,20839"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dc43d647cd71a26bfd564415b69e8be4\\transformed\\play-services-base-18.0.1\\res\\values-ro\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,452,577,682,829,957,1076,1181,1339,1445,1600,1728,1870,2032,2099,2162", "endColumns": "102,155,124,104,146,127,118,104,157,105,154,127,141,161,66,62,77", "endOffsets": "295,451,576,681,828,956,1075,1180,1338,1444,1599,1727,1869,2031,2098,2161,2239"}, "to": {"startLines": "68,69,70,71,72,73,74,75,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5308,5415,5575,5704,5813,5964,6096,6219,6472,6634,6744,6903,7035,7181,7347,7418,7485", "endColumns": "106,159,128,108,150,131,122,108,161,109,158,131,145,165,70,66,81", "endOffsets": "5410,5570,5699,5808,5959,6091,6214,6323,6629,6739,6898,7030,7176,7342,7413,7480,7562"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "55,56,57,58,59,60,61,244", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4015,4113,4215,4315,4414,4516,4625,20055", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "4108,4210,4310,4409,4511,4620,4737,20151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,276,363,464,585,669,735,830,904,964,1048,1114,1172,1245,1308,1364,1483,1540,1601,1657,1731,1876,1962,2046,2149,2231,2314,2404,2471,2537,2610,2688,2776,2847,2924,2998,3070,3161,3235,3330,3428,3502,3582,3683,3736,3802,3891,3981,4043,4107,4170,4282,4395,4505,4617", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,86,100,120,83,65,94,73,59,83,65,57,72,62,55,118,56,60,55,73,144,85,83,102,81,82,89,66,65,72,77,87,70,76,73,71,90,73,94,97,73,79,100,52,65,88,89,61,63,62,111,112,109,111,78", "endOffsets": "271,358,459,580,664,730,825,899,959,1043,1109,1167,1240,1303,1359,1478,1535,1596,1652,1726,1871,1957,2041,2144,2226,2309,2399,2466,2532,2605,2683,2771,2842,2919,2993,3065,3156,3230,3325,3423,3497,3577,3678,3731,3797,3886,3976,4038,4102,4165,4277,4390,4500,4612,4691"}, "to": {"startLines": "21,54,62,63,64,90,142,147,152,153,154,155,156,157,158,159,160,161,162,163,164,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "902,3928,4742,4843,4964,7961,11885,12368,12762,12822,12906,12972,13030,13103,13166,13222,13341,13398,13459,13515,13589,13962,14048,14132,14235,14317,14400,14490,14557,14623,14696,14774,14862,14933,15010,15084,15156,15247,15321,15416,15514,15588,15668,15769,15822,15888,15977,16067,16129,16193,16256,16368,16481,16591,18027", "endLines": "25,54,62,63,64,90,142,147,152,153,154,155,156,157,158,159,160,161,162,163,164,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,217", "endColumns": "12,86,100,120,83,65,94,73,59,83,65,57,72,62,55,118,56,60,55,73,144,85,83,102,81,82,89,66,65,72,77,87,70,76,73,71,90,73,94,97,73,79,100,52,65,88,89,61,63,62,111,112,109,111,78", "endOffsets": "1118,4010,4838,4959,5043,8022,11975,12437,12817,12901,12967,13025,13098,13161,13217,13336,13393,13454,13510,13584,13729,14043,14127,14230,14312,14395,14485,14552,14618,14691,14769,14857,14928,15005,15079,15151,15242,15316,15411,15509,15583,15663,15764,15817,15883,15972,16062,16124,16188,16251,16363,16476,16586,16698,18101"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1c6965b61d3737d12e0f0d1e87d21cde\\transformed\\ui-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,383,485,573,651,738,829,911,999,1089,1162,1236,1315,1390,1467,1534", "endColumns": "96,83,96,101,87,77,86,90,81,87,89,72,73,78,74,76,66,114", "endOffsets": "197,281,378,480,568,646,733,824,906,994,1084,1157,1231,1310,1385,1462,1529,1644"}, "to": {"startLines": "65,66,87,88,89,150,151,204,205,220,221,232,236,239,241,246,247,249", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5048,5145,7674,7771,7873,12597,12675,16874,16965,18281,18369,19113,19433,19664,19821,20229,20306,20449", "endColumns": "96,83,96,101,87,77,86,90,81,87,89,72,73,78,74,76,66,114", "endOffsets": "5140,5224,7766,7868,7956,12670,12757,16960,17042,18364,18454,19181,19502,19738,19891,20301,20368,20559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\659eba141f87d2afd24e0f28711a233e\\transformed\\play-services-ads-23.6.0\\res\\values-ro\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,244,290,358,423,491,592,660,788,878,999,1049,1099,1213,1294,1339,1432,1467,1501,1561,1643,1685", "endColumns": "44,45,67,64,67,100,67,127,89,120,49,49,113,80,44,92,34,33,59,81,41,55", "endOffsets": "243,289,357,422,490,591,659,787,877,998,1048,1098,1212,1293,1338,1431,1466,1500,1560,1642,1684,1740"}, "to": {"startLines": "201,202,203,206,207,208,209,210,211,212,213,214,215,216,223,224,225,226,227,228,229,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16703,16752,16802,17047,17116,17188,17293,17365,17497,17591,17716,17770,17824,17942,18528,18577,18674,18713,18751,18815,18901,20844", "endColumns": "48,49,71,68,71,104,71,131,93,124,53,53,117,84,48,96,38,37,63,85,45,59", "endOffsets": "16747,16797,16869,17111,17183,17288,17360,17492,17586,17711,17765,17819,17937,18022,18572,18669,18708,18746,18810,18896,18942,20899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495d9b066df23cebe365f0b1f0fc7029\\transformed\\exoplayer-ui-2.18.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,582,852,939,1028,1116,1212,1308,1383,1449,1548,1645,1716,1781,1844,1913,2029,2144,2260,2336,2416,2485,2561,2659,2759,2824,2887,2940,2998,3046,3107,3171,3241,3306,3375,3436,3494,3560,3624,3690,3742,3804,3880,3956", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,86,88,87,95,95,74,65,98,96,70,64,62,68,115,114,115,75,79,68,75,97,99,64,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "281,577,847,934,1023,1111,1207,1303,1378,1444,1543,1640,1711,1776,1839,1908,2024,2139,2255,2331,2411,2480,2556,2654,2754,2819,2882,2935,2993,3041,3102,3166,3236,3301,3370,3431,3489,3555,3619,3685,3737,3799,3875,3951,4008"}, "to": {"startLines": "2,11,16,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,632,8027,8114,8203,8291,8387,8483,8558,8624,8723,8820,8891,8956,9019,9088,9204,9319,9435,9511,9591,9660,9736,9834,9934,9999,10759,10812,10870,10918,10979,11043,11113,11178,11247,11308,11366,11432,11496,11562,11614,11676,11752,11828", "endLines": "10,15,20,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "endColumns": "17,12,12,86,88,87,95,95,74,65,98,96,70,64,62,68,115,114,115,75,79,68,75,97,99,64,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "331,627,897,8109,8198,8286,8382,8478,8553,8619,8718,8815,8886,8951,9014,9083,9199,9314,9430,9506,9586,9655,9731,9829,9929,9994,10057,10807,10865,10913,10974,11038,11108,11173,11242,11303,11361,11427,11491,11557,11609,11671,11747,11823,11880"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46429bd1dac210616d37d633e605f2e8\\transformed\\browser-1.8.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,377", "endColumns": "106,101,112,102", "endOffsets": "157,259,372,475"}, "to": {"startLines": "86,143,144,145", "startColumns": "4,4,4,4", "startOffsets": "7567,11980,12082,12195", "endColumns": "106,101,112,102", "endOffsets": "7669,12077,12190,12293"}}]}]}